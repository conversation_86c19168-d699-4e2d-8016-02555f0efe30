#!/usr/bin/env python3
"""
简化版本的skip_frame数据提取脚本
"""

import re
from datetime import datetime

def extract_skip_frame_data():
    """提取skip_frame数据"""
    data = []
    
    # 正则表达式匹配包含skip_frame的行
    pattern = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\].*?skip_frame:(\d+)'
    
    # 尝试不同的编码
    encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            print(f"尝试编码: {encoding}")
            with open('pilot.log', 'r', encoding=encoding) as file:
                line_count = 0
                for line in file:
                    line_count += 1
                    if 'skip_frame:' in line:
                        match = re.search(pattern, line)
                        if match:
                            timestamp_str = match.group(1)
                            skip_frame = int(match.group(2))
                            
                            try:
                                dt = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S.%f')
                                data.append((dt, skip_frame))
                            except ValueError:
                                continue
                
                print(f"成功读取文件，共{line_count}行，找到{len(data)}条skip_frame记录")
                break
                
        except UnicodeDecodeError as e:
            print(f"编码{encoding}失败: {e}")
            continue
        except Exception as e:
            print(f"读取文件出错: {e}")
            return []
    
    return data

def save_data(data):
    """保存数据到文件"""
    if not data:
        print("没有数据可保存")
        return
    
    # 保存为CSV
    with open('skip_frame_data.csv', 'w') as f:
        f.write('时间,Skip_Frame\n')
        for dt, skip_frame in data:
            f.write(f'{dt.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]},{skip_frame}\n')
    
    # 保存为简单文本
    with open('skip_frame_summary.txt', 'w') as f:
        f.write('Skip Frame 数据摘要\n')
        f.write('=' * 30 + '\n\n')
        
        skip_values = [item[1] for item in data]
        f.write(f'数据点总数: {len(data)}\n')
        f.write(f'时间范围: {data[0][0]} 到 {data[-1][0]}\n')
        f.write(f'最大值: {max(skip_values)}\n')
        f.write(f'最小值: {min(skip_values)}\n')
        f.write(f'平均值: {sum(skip_values)/len(skip_values):.2f}\n\n')
        
        f.write('前20个数据点:\n')
        for i, (dt, skip_frame) in enumerate(data[:20]):
            f.write(f'{i+1:2d}. {dt.strftime("%H:%M:%S.%f")[:-3]} -> {skip_frame}\n')
        
        if len(data) > 20:
            f.write('\n后20个数据点:\n')
            for i, (dt, skip_frame) in enumerate(data[-20:], len(data)-19):
                f.write(f'{i:2d}. {dt.strftime("%H:%M:%S.%f")[:-3]} -> {skip_frame}\n')

if __name__ == "__main__":
    print("开始提取skip_frame数据...")
    data = extract_skip_frame_data()
    
    if data:
        save_data(data)
        print(f"数据提取完成！")
        print(f"生成文件:")
        print(f"- skip_frame_data.csv (可用Excel打开)")
        print(f"- skip_frame_summary.txt (数据摘要)")
    else:
        print("未找到数据")
