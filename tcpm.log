[    0.371485] Setting usb_comm capable false
[    0.372620] Setting voltage/current limit 0 mV 0 mA
[    0.372623] polarity 0
[    0.372624] Requesting mux state 0, usb-role 0, orientation 0
[    0.372839] state change INVALID_STATE -> SNK_UNATTACHED [rev1 NONE_AMS]
[    0.372846] CC1: 0 -> 0, CC2: 0 -> 0 [state SNK_UNATTACHED, polarity 0, disconnected]
[    0.372850] 2-0022: registered
[    0.372852] Setting usb_comm capable false
[    0.374005] Setting voltage/current limit 0 mV 0 mA
[    0.374016] polarity 0
[    0.374018] Requesting mux state 0, usb-role 0, orientation 0
[    0.374239] cc:=2
[    0.375576] pending state change PORT_RESET -> PORT_RESET_WAIT_OFF @ 100 ms [rev1 NONE_AMS]
[    0.375582] state change PORT_RESET -> PORT_RESET_WAIT_OFF [delayed 100 ms]
[    0.375585] state change PORT_RESET_WAIT_OFF -> SNK_UNATTACHED [rev1 NONE_AMS]
[    0.375588] Start toggling
[    0.378256] VBUS on
[    0.383507] CC1: 0 -> 4, CC2: 0 -> 0 [state TOGGLING, polarity 0, connected]
[    0.383512] state change TOGGLING -> SNK_ATTACH_WAIT [rev1 NONE_AMS]
[    0.383515] pending state change SNK_ATTACH_WAIT -> SNK_DEBOUNCED @ 200 ms [rev1 NONE_AMS]
[    0.583538] state change SNK_ATTACH_WAIT -> SNK_DEBOUNCED [delayed 200 ms]
[    0.583545] state change SNK_DEBOUNCED -> SNK_ATTACHED [rev1 NONE_AMS]
[    0.583550] polarity 0
[    0.583553] Requesting mux state 1, usb-role 2, orientation 1
[    0.583837] state change SNK_ATTACHED -> SNK_STARTUP [rev1 NONE_AMS]
[    0.583859] state change SNK_STARTUP -> SNK_DISCOVERY [rev2 NONE_AMS]
[    0.583862] Setting voltage/current limit 5000 mV 1500 mA
[    0.583870] vbus=0 charge:=1
[    0.583875] state change SNK_DISCOVERY -> SNK_WAIT_CAPABILITIES [rev2 NONE_AMS]
[    0.585054] pending state change SNK_WAIT_CAPABILITIES -> HARD_RESET_SEND @ 620 ms [rev2 NONE_AMS]
[    0.696750] PD RX, header: 0x11a1 [1], 1 objects
[    0.696756] PD RX, object:0x37019096
[    0.696762]  PDO 0: type 0, 5000 mV, 1500 mA [RSHUD]
[    0.696923] state change SNK_WAIT_CAPABILITIES -> SNK_NEGOTIATE_CAPABILITIES [rev2 POWER_NEGOTIATION]
[    0.696934] Setting usb_comm capable true
[    0.696951] cc=2 cc1=4 cc2=0 vbus=0 vconn=sink polarity=0
[    0.696954] Requesting PDO 0: 5000 mV, 1500 mA [mismatch]
[    0.696958] PD TX, header: 0x1042
[    0.700172] PD TX complete, status: 0
[    0.700195] pending state change SNK_NEGOTIATE_CAPABILITIES -> HARD_RESET_SEND @ 60 ms [rev2 POWER_NEGOTIATION]
[    0.704783] PD RX, header: 0x363 [1], 0 objects
[    0.704792] state change SNK_NEGOTIATE_CAPABILITIES -> SNK_TRANSITION_SINK [rev2 POWER_NEGOTIATION]
[    0.704801] pending state change SNK_TRANSITION_SINK -> HARD_RESET_SEND @ 500 ms [rev2 POWER_NEGOTIATION]
[    0.740602] PD RX, header: 0x566 [1], 0 objects
[    0.740611] Setting voltage/current limit 5000 mV 1500 mA
[    0.740625] state change SNK_TRANSITION_SINK -> SNK_READY [rev2 POWER_NEGOTIATION]
[    0.740807] AMS POWER_NEGOTIATION finished
[    0.743438] PD RX, header: 0x1761 [1], 1 objects
[    0.743444] PD RX, object:0x26019096
[    0.743451]  PDO 0: type 0, 5000 mV, 1500 mA [RUD]
[    0.743589] state change SNK_READY -> SNK_NEGOTIATE_CAPABILITIES [rev2 POWER_NEGOTIATION]
[    0.743595] Setting usb_comm capable true
[    0.743611] cc=2 cc1=4 cc2=0 vbus=0 vconn=sink polarity=0
[    0.743613] Requesting PDO 0: 5000 mV, 1500 mA [mismatch]
[    0.743616] PD TX, header: 0x1242
[    0.746734] PD TX complete, status: 0
[    0.746756] pending state change SNK_NEGOTIATE_CAPABILITIES -> HARD_RESET_SEND @ 60 ms [rev2 POWER_NEGOTIATION]
[    0.746871] CC1: 4 -> 5, CC2: 0 -> 0 [state SNK_NEGOTIATE_CAPABILITIES, polarity 0, connected]
[    0.751453] PD RX, header: 0x963 [1], 0 objects
[    0.751466] state change SNK_NEGOTIATE_CAPABILITIES -> SNK_TRANSITION_SINK [rev2 POWER_NEGOTIATION]
[    0.751477] pending state change SNK_TRANSITION_SINK -> HARD_RESET_SEND @ 500 ms [rev2 POWER_NEGOTIATION]
[    0.787142] PD RX, header: 0xb66 [1], 0 objects
[    0.787147] Setting voltage/current limit 5000 mV 1500 mA
[    0.787161] state change SNK_TRANSITION_SINK -> SNK_READY [rev2 POWER_NEGOTIATION]
[    0.787170] AMS POWER_NEGOTIATION finished
[    0.789757] PD RX, header: 0xd68 [1], 0 objects
[    0.789770] PD TX, header: 0x1444
[    0.793109] PD TX complete, status: 0
[    0.793122] AMS GET_SINK_CAPABILITIES finished
[    0.798636] PD RX, header: 0x1f6f [1], 1 objects
[    0.798640] PD RX, object:0xff008001
[    0.798644] Rx VDM cmd 0xff008001 type 0 cmd 1 len 1 adev           (null)
[    0.798658] tcpm_queue_vdm
[    0.798661] vdm_run_state_machine vdm_state:1
[    0.798664] AMS DISCOVER_IDENTITY start
[    0.798667] vdm_run_state_machine vdm_state:4
[    0.798669] PD TX, header: 0x564f
[    0.802373] PD TX complete, status: 0
[    0.802390] AMS DISCOVER_IDENTITY finished
[    0.802401] vdm_run_state_machine vdm_state:2
[    0.802403] vdm_run_state_machine vdm_state:-1
[    0.808391] PD RX, header: 0x116f [1], 1 objects
[    0.808395] PD RX, object:0xff008002
[    0.808399] Rx VDM cmd 0xff008002 type 0 cmd 2 len 1 adev           (null)
[    0.808409] svid 0xff01
[    0.808412] tcpm_queue_vdm
[    0.808419] vdm_run_state_machine vdm_state:1
[    0.808422] AMS DISCOVER_SVIDS start
[    0.808425] vdm_run_state_machine vdm_state:4
[    0.808427] PD TX, header: 0x284f
[    0.811941] PD TX complete, status: 0
[    0.811965] AMS DISCOVER_SVIDS finished
[    0.811974] vdm_run_state_machine vdm_state:2
[    0.811975] vdm_run_state_machine vdm_state:-1
[    0.816960] PD RX, header: 0x136f [1], 1 objects
[    0.816965] PD RX, object:0xff018003
[    0.816979] Rx VDM cmd 0xff018003 type 0 cmd 3 len 1 adev           (null)
[    0.816992] SRC SVID 1: 0xff01
[    0.816995] tcpm_queue_vdm
[    0.816999] vdm_run_state_machine vdm_state:1
[    0.817002] AMS DISCOVER_MODES start
[    0.817006] vdm_run_state_machine vdm_state:4
[    0.817007] PD TX, header: 0x2a4f
[    0.821752] PD TX complete, status: 0
[    0.821766] AMS DISCOVER_MODES finished
[    0.821779] vdm_run_state_machine vdm_state:2
[    0.821781] vdm_run_state_machine vdm_state:-1
[    0.826952] PD RX, header: 0x156f [1], 1 objects
[    0.826960] PD RX, object:0xff018104
[    0.826967] Rx VDM cmd 0xff018104 type 0 cmd 4 len 1 adev ffffffc030b8f408
[    0.826980]  Alternate mode 0: SVID 0xff01, VDO 1: 0x00000405
[    0.827364] tcpm_queue_vdm
[    0.827379] vdm_run_state_machine vdm_state:1
[    0.827384] AMS DFP_TO_UFP_ENTER_MODE start
[    0.827387] vdm_run_state_machine vdm_state:4
[    0.827389] PD TX, header: 0x1c4f
[    0.830759] PD TX complete, status: 0
[    0.830785] AMS DFP_TO_UFP_ENTER_MODE finished
[    0.830792] vdm_run_state_machine vdm_state:2
[    0.830794] vdm_run_state_machine vdm_state:-1
[    0.836293] PD RX, header: 0x276f [1], 2 objects
[    0.836297] PD RX, object:0xff018110
[    0.836298] PD RX, object:0x1
[    0.836301] Rx VDM cmd 0xff018110 type 0 cmd 16 len 2 adev ffffffc030b8f408
[    0.836350] tcpm_queue_vdm
[    0.836355] vdm_run_state_machine vdm_state:1
[    0.836358] AMS STRUCTURED_VDMS start
[    0.836360] vdm_run_state_machine vdm_state:4
[    0.836362] PD TX, header: 0x2e4f
[    0.839630] PD TX complete, status: 0
[    0.839655] AMS STRUCTURED_VDMS finished
[    0.839662] vdm_run_state_machine vdm_state:2
[    0.839663] vdm_run_state_machine vdm_state:-1
[    0.856380] PD RX, header: 0x296f [1], 2 objects
[    0.856387] PD RX, object:0xff018111
[    0.856389] PD RX, object:0x406
[    0.856392] Rx VDM cmd 0xff018111 type 0 cmd 17 len 2 adev ffffffc030b8f408
[    0.856461] tcpm_queue_vdm
[    0.856548] vdm_run_state_machine vdm_state:1
[    0.856550] AMS STRUCTURED_VDMS start
[    0.856554] vdm_run_state_machine vdm_state:4
[    0.856556] PD TX, header: 0x104f
[    0.859884] PD TX complete, status: 0
[    0.859895] AMS STRUCTURED_VDMS finished
[    0.859904] vdm_run_state_machine vdm_state:2
[    0.859906] vdm_run_state_machine vdm_state:-1
[    0.879255] tcpm_queue_vdm
[    0.879318] vdm_run_state_machine vdm_state:1
[    0.879321] AMS ATTENTION start
[    0.879324] vdm_run_state_machine vdm_state:4
[    0.879326] PD TX, header: 0x224f
[    0.882794] PD TX complete, status: 0
[    0.882883] AMS ATTENTION finished
[    0.913005] vdm_run_state_machine vdm_state:2
[    0.913009] vdm_run_state_machine vdm_state:-1
