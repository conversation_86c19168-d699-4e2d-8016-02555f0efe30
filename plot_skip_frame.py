#!/usr/bin/env python3
"""
脚本用于从pilot.log文件中提取skip_frame数据并绘制时间序列图
"""

import re
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import pandas as pd

def parse_pilot_log(log_file_path):
    """
    解析pilot.log文件，提取时间戳和skip_frame值
    
    Args:
        log_file_path (str): pilot.log文件路径
        
    Returns:
        tuple: (timestamps, skip_frame_values) 两个列表
    """
    timestamps = []
    skip_frame_values = []
    
    # 正则表达式匹配包含skip_frame的行
    # 示例行: Jan 25 00:00:01 XREAL[529]: [2027-01-25 00:00:01.515] [829] [DEBUG] [Flinger] frame_not_shown:0 skip_frame:0 addr_mismatch:0 pose_error:0
    pattern = r'(\w{3} \d{2} \d{2}:\d{2}:\d{2}).*?\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\].*?skip_frame:(\d+)'
    
    try:
        with open(log_file_path, 'r', encoding='utf-8') as file:
            for line_num, line in enumerate(file, 1):
                match = re.search(pattern, line)
                if match:
                    # 提取时间戳和skip_frame值
                    system_time = match.group(1)
                    app_timestamp = match.group(2)
                    skip_frame = int(match.group(3))
                    
                    # 使用应用程序时间戳（更精确）
                    try:
                        dt = datetime.strptime(app_timestamp, '%Y-%m-%d %H:%M:%S.%f')
                        timestamps.append(dt)
                        skip_frame_values.append(skip_frame)
                    except ValueError:
                        print(f"警告: 第{line_num}行时间戳格式错误: {app_timestamp}")
                        continue
                        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {log_file_path}")
        return [], []
    except Exception as e:
        print(f"错误: 读取文件时发生异常: {e}")
        return [], []
    
    print(f"成功解析 {len(timestamps)} 条skip_frame记录")
    return timestamps, skip_frame_values

def plot_skip_frame(timestamps, skip_frame_values, output_file=None):
    """
    绘制skip_frame随时间变化的折线图
    
    Args:
        timestamps (list): 时间戳列表
        skip_frame_values (list): skip_frame值列表
        output_file (str, optional): 输出图片文件路径
    """
    if not timestamps or not skip_frame_values:
        print("错误: 没有数据可以绘制")
        return
    
    # 创建图形和轴
    plt.figure(figsize=(15, 8))
    
    # 绘制折线图
    plt.plot(timestamps, skip_frame_values, 'b-', linewidth=1.5, alpha=0.8, label='skip_frame')
    
    # 添加散点以突出数据点
    plt.scatter(timestamps, skip_frame_values, c='red', s=20, alpha=0.6, zorder=5)
    
    # 设置图表标题和标签
    plt.title('Skip Frame 随时间变化趋势图', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('时间', fontsize=12)
    plt.ylabel('Skip Frame 数量', fontsize=12)
    
    # 设置网格
    plt.grid(True, alpha=0.3, linestyle='--')
    
    # 格式化x轴时间显示
    ax = plt.gca()
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M:%S'))
    ax.xaxis.set_major_locator(mdates.MinuteLocator(interval=2))  # 每2分钟一个刻度
    plt.xticks(rotation=45)
    
    # 设置y轴
    plt.ylim(bottom=0)  # y轴从0开始
    
    # 添加图例
    plt.legend()
    
    # 调整布局
    plt.tight_layout()
    
    # 添加统计信息
    if skip_frame_values:
        max_skip = max(skip_frame_values)
        min_skip = min(skip_frame_values)
        avg_skip = sum(skip_frame_values) / len(skip_frame_values)
        
        stats_text = f'最大值: {max_skip}\n最小值: {min_skip}\n平均值: {avg_skip:.2f}\n数据点: {len(skip_frame_values)}'
        plt.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # 保存或显示图片
    if output_file:
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"图表已保存到: {output_file}")
    else:
        plt.show()

def main():
    """主函数"""
    log_file = 'pilot.log'
    output_file = 'skip_frame_plot.png'
    
    print("开始解析pilot.log文件...")
    timestamps, skip_frame_values = parse_pilot_log(log_file)
    
    if timestamps and skip_frame_values:
        print("开始绘制图表...")
        plot_skip_frame(timestamps, skip_frame_values, output_file)
        
        # 打印一些基本统计信息
        print(f"\n=== 统计信息 ===")
        print(f"数据点总数: {len(skip_frame_values)}")
        print(f"时间范围: {timestamps[0]} 到 {timestamps[-1]}")
        print(f"skip_frame最大值: {max(skip_frame_values)}")
        print(f"skip_frame最小值: {min(skip_frame_values)}")
        print(f"skip_frame平均值: {sum(skip_frame_values)/len(skip_frame_values):.2f}")
        
        # 计算skip_frame变化趋势
        if len(skip_frame_values) > 1:
            increases = sum(1 for i in range(1, len(skip_frame_values)) 
                          if skip_frame_values[i] > skip_frame_values[i-1])
            print(f"skip_frame增长次数: {increases}")
    else:
        print("未找到有效的skip_frame数据")

if __name__ == "__main__":
    main()
