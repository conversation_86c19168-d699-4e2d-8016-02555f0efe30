[    0.369283] sw reset
[    0.370193] sw reset
[    0.371482] aw35615 device ID: 0x91
[    0.372615] pd := off
[    0.372616] vbus is already Off
[    0.372617] charge is already Off
[    0.372619] vconn is already Off
[    0.372834] pd header := Sink, Device
[    0.372845] cc1=Open, cc2=Open
[    0.373997] pd := off
[    0.374000] vbus is already Off
[    0.374001] charge is already Off
[    0.374003] vconn is already Off
[    0.374232] pd header := Sink, Device
[    0.374241] cc := Rd
[    0.376730] start drp toggling
[    0.377477] IRQ: 0x80, a: 0x00, b: 0x00, status0: 0x83, status1: 0x28
[    0.377479] IRQ: VBUS_OK, vbus=On
[    0.377486] gpio_intn_value:0
[    0.377596] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.378138] IRQ: 0x80, a: 0x00, b: 0x00, status0: 0x83, status1: 0x28
[    0.378140] IRQ: VBUS_OK, vbus=On
[    0.378142] gpio_intn_value:1
[    0.378251] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.381293] IRQ: 0x00, a: 0x40, b: 0x00, status0: 0x82, status1: 0x28
[    0.381297] IRQ: TOGDONE
[    0.382735] detected cc1=Rp-1.5, cc2=Open
[    0.382737] gpio_intn_value:0
[    0.382847] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.383388] IRQ: 0x01, a: 0x00, b: 0x00, status0: 0x82, status1: 0x28
[    0.383390] gpio_intn_value:1
[    0.383499] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.383504] cc1=Rp-1.5, cc2=Open
[    0.512483] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.512495] IRQ: BC_LVL, handler pending
[    0.512501] gpio_intn_value:0
[    0.512610] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.513150] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.513153] IRQ: BC_LVL, handler pending
[    0.513154] gpio_intn_value:1
[    0.513263] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.514135] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc0, status1: 0x08
[    0.514137] IRQ: BC_LVL, handler pending
[    0.514139] gpio_intn_value:0
[    0.514250] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.514790] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.514792] IRQ: BC_LVL, handler pending
[    0.514794] gpio_intn_value:1
[    0.514902] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.515759] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.515761] IRQ: BC_LVL, handler pending
[    0.515762] gpio_intn_value:0
[    0.515871] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.516409] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.516411] IRQ: BC_LVL, handler pending
[    0.516413] gpio_intn_value:1
[    0.516521] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.549098] BC_LVL handler, status0=0x92
[    0.583833] pd header := Sink, Device
[    0.583872] vbus is already Off
[    0.585048] pd := on
[    0.694733] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.694738] IRQ: BC_LVL, handler pending
[    0.694745] gpio_intn_value:0
[    0.694854] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.695403] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x08
[    0.695406] IRQ: BC_LVL, handler pending
[    0.695410] gpio_intn_value:0
[    0.695525] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.696101] IRQ: 0x41, a: 0x00, b: 0x01, status0: 0x92, status1: 0x08
[    0.696107] IRQ: BC_LVL, handler pending
[    0.696111] IRQ: PD sent good CRC
[    0.696735] PD message header: 11a1 len:4 crc:fa9c4f1c
[    0.696748] gpio_intn_value:1
[    0.696860] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.697688] sending PD message header: 1042
[    0.697695] sending PD message len: 4
[    0.698331] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x28
[    0.698341] IRQ: BC_LVL, handler pending
[    0.698352] gpio_intn_value:0
[    0.698467] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.699039] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.699045] IRQ: BC_LVL, handler pending
[    0.699053] gpio_intn_value:0
[    0.699165] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.699725] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0x92, status1: 0x08
[    0.699733] IRQ: BC_LVL, handler pending
[    0.699739] IRQ: PD tx success
[    0.700166] PD message header: 161 len:0 crc:4a38788f
[    0.700184] gpio_intn_value:1
[    0.700296] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.703596] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x08
[    0.703604] IRQ: BC_LVL, handler pending
[    0.703612] gpio_intn_value:0
[    0.703723] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.704312] IRQ: 0x51, a: 0x00, b: 0x01, status0: 0x92, status1: 0x08
[    0.704317] IRQ: BC_LVL, handler pending
[    0.704323] IRQ: PD sent good CRC
[    0.704755] PD message header: 363 len:0 crc:96007b21
[    0.704770] gpio_intn_value:0
[    0.704888] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.705435] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x92, status1: 0x28
[    0.705439] IRQ: BC_LVL, handler pending
[    0.705447] gpio_intn_value:1
[    0.705559] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.739418] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x08
[    0.739430] IRQ: BC_LVL, handler pending
[    0.739436] gpio_intn_value:0
[    0.739546] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.740089] IRQ: 0x51, a: 0x00, b: 0x01, status0: 0x92, status1: 0x08
[    0.740093] IRQ: BC_LVL, handler pending
[    0.740095] IRQ: PD sent good CRC
[    0.740580] PD message header: 566 len:0 crc:2142a51
[    0.740597] gpio_intn_value:0
[    0.740741] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.741331] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc2, status1: 0x08
[    0.741335] IRQ: BC_LVL, handler pending
[    0.741344] gpio_intn_value:0
[    0.741458] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.742034] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x93, status1: 0x08
[    0.742038] IRQ: BC_LVL, handler pending
[    0.742043] gpio_intn_value:0
[    0.742158] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.742802] IRQ: 0x41, a: 0x00, b: 0x01, status0: 0x93, status1: 0x08
[    0.742810] IRQ: BC_LVL, handler pending
[    0.742813] IRQ: PD sent good CRC
[    0.743423] PD message header: 1761 len:4 crc:e8484fa
[    0.743436] gpio_intn_value:1
[    0.743548] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.743671] BC_LVL handler, status0=0x93
[    0.743673] cc1: Rp-1.5 -> Rp-3.0
[    0.744296] sending PD message header: 1242
[    0.744298] sending PD message len: 4
[    0.744930] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x20
[    0.744933] IRQ: BC_LVL, handler pending
[    0.744938] gpio_intn_value:0
[    0.745047] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.745586] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc2, status1: 0x08
[    0.745588] IRQ: BC_LVL, handler pending
[    0.745590] gpio_intn_value:0
[    0.745703] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.746265] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0x93, status1: 0x08
[    0.746271] IRQ: BC_LVL, handler pending
[    0.746275] IRQ: PD tx success
[    0.746724] PD message header: 361 len:0 crc:a43619a3
[    0.746745] gpio_intn_value:1
[    0.746861] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.746867] cc1=Rp-3.0, cc2=Open
[    0.750246] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x08
[    0.750253] IRQ: BC_LVL, handler pending
[    0.750260] gpio_intn_value:0
[    0.750371] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.750956] IRQ: 0x51, a: 0x00, b: 0x01, status0: 0x93, status1: 0x08
[    0.750961] IRQ: BC_LVL, handler pending
[    0.750965] IRQ: PD sent good CRC
[    0.751430] PD message header: 963 len:0 crc:76d5923f
[    0.751451] gpio_intn_value:0
[    0.751565] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.752130] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x93, status1: 0x28
[    0.752136] IRQ: BC_LVL, handler pending
[    0.752146] gpio_intn_value:1
[    0.752262] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.785983] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x93, status1: 0x08
[    0.785990] IRQ: BC_LVL, handler pending
[    0.785999] gpio_intn_value:0
[    0.786109] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.786654] IRQ: 0x51, a: 0x00, b: 0x01, status0: 0x93, status1: 0x08
[    0.786660] IRQ: BC_LVL, handler pending
[    0.786664] IRQ: PD sent good CRC
[    0.787113] PD message header: b66 len:0 crc:e5ac0756
[    0.787125] gpio_intn_value:0
[    0.787235] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.787869] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc2, status1: 0x08
[    0.787876] IRQ: BC_LVL, handler pending
[    0.787884] gpio_intn_value:0
[    0.787996] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.788565] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x93, status1: 0x08
[    0.788576] IRQ: BC_LVL, handler pending
[    0.788582] gpio_intn_value:0
[    0.788695] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.789268] IRQ: 0x41, a: 0x00, b: 0x01, status0: 0x93, status1: 0x08
[    0.789279] IRQ: BC_LVL, handler pending
[    0.789283] IRQ: PD sent good CRC
[    0.789741] PD message header: d68 len:0 crc:924c8fed
[    0.789755] gpio_intn_value:1
[    0.789869] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.790495] sending PD message header: 1444
[    0.790503] sending PD message len: 4
[    0.791236] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd0, status1: 0x28
[    0.791247] IRQ: BC_LVL, handler pending
[    0.791253] gpio_intn_value:0
[    0.791385] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.791943] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x93, status1: 0x08
[    0.791951] IRQ: BC_LVL, handler pending
[    0.791958] gpio_intn_value:0
[    0.792069] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.792633] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0x93, status1: 0x08
[    0.792637] IRQ: BC_LVL, handler pending
[    0.792640] IRQ: PD tx success
[    0.793100] PD message header: 561 len:0 crc:4d55bc96
[    0.793120] gpio_intn_value:1
[    0.793243] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.796481] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc0, status1: 0x08
[    0.796490] IRQ: BC_LVL, handler pending
[    0.796500] gpio_intn_value:0
[    0.796610] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.797177] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x93, status1: 0x08
[    0.797182] IRQ: BC_LVL, handler pending
[    0.797188] gpio_intn_value:0
[    0.797339] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.797933] IRQ: 0x41, a: 0x00, b: 0x01, status0: 0x93, status1: 0x08
[    0.797946] IRQ: BC_LVL, handler pending
[    0.797950] IRQ: PD sent good CRC
[    0.798578] PD message header: 1f6f len:4 crc:de5b8f17
[    0.798603] gpio_intn_value:1
[    0.798726] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.799852] sending PD message header: 564f
[    0.799865] sending PD message len: 20
[    0.800483] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x20
[    0.800492] IRQ: BC_LVL, handler pending
[    0.800501] gpio_intn_value:0
[    0.800616] aw35615_i2c_read AW_REG_STATUS1 :0x20
[    0.801204] IRQ: 0x01, a: 0x00, b: 0x00, status0: 0xd0, status1: 0x28
[    0.801214] IRQ: BC_LVL, handler pending
[    0.801222] gpio_intn_value:0
[    0.801343] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.801914] IRQ: 0x41, a: 0x04, b: 0x00, status0: 0x93, status1: 0x08
[    0.801926] IRQ: BC_LVL, handler pending
[    0.801931] IRQ: PD tx success
[    0.802365] PD message header: 761 len:0 crc:a35bddba
[    0.802387] gpio_intn_value:0
[    0.802500] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.803061] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x93, status1: 0x28
[    0.803075] IRQ: BC_LVL, handler pending
[    0.803087] gpio_intn_value:1
[    0.803201] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.806307] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.806319] IRQ: BC_LVL, handler pending
[    0.806330] gpio_intn_value:0
[    0.806447] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.807014] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x93, status1: 0x08
[    0.807024] IRQ: BC_LVL, handler pending
[    0.807034] gpio_intn_value:0
[    0.807148] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.807717] IRQ: 0x41, a: 0x00, b: 0x01, status0: 0x93, status1: 0x08
[    0.807733] IRQ: BC_LVL, handler pending
[    0.807740] IRQ: PD sent good CRC
[    0.808362] PD message header: 116f len:4 crc:73de9e98
[    0.808383] gpio_intn_value:1
[    0.808498] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.809228] sending PD message header: 284f
[    0.809239] sending PD message len: 8
[    0.809910] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x20
[    0.809918] IRQ: BC_LVL, handler pending
[    0.809929] gpio_intn_value:0
[    0.810045] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.810692] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x93, status1: 0x08
[    0.810705] IRQ: BC_LVL, handler pending
[    0.810712] gpio_intn_value:0
[    0.810869] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.811443] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0x93, status1: 0x08
[    0.811457] IRQ: BC_LVL, handler pending
[    0.811462] IRQ: PD tx success
[    0.811925] PD message header: 961 len:0 crc:44e3f0bd
[    0.811956] gpio_intn_value:1
[    0.812110] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.815439] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.815452] IRQ: BC_LVL, handler pending
[    0.815460] gpio_intn_value:0
[    0.815584] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.816233] IRQ: 0x51, a: 0x00, b: 0x01, status0: 0x93, status1: 0x08
[    0.816243] IRQ: BC_LVL, handler pending
[    0.816247] IRQ: PD sent good CRC
[    0.816932] PD message header: 136f len:4 crc:a8b99bdc
[    0.816957] gpio_intn_value:0
[    0.817085] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.817680] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x93, status1: 0x28
[    0.817691] IRQ: BC_LVL, handler pending
[    0.817704] gpio_intn_value:1
[    0.817861] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.818697] sending PD message header: 2a4f
[    0.818703] sending PD message len: 8
[    0.819379] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x20
[    0.819388] IRQ: BC_LVL, handler pending
[    0.819406] gpio_intn_value:0
[    0.819534] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.820354] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x93, status1: 0x08
[    0.820368] IRQ: BC_LVL, handler pending
[    0.820377] gpio_intn_value:0
[    0.820567] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.821251] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0x93, status1: 0x08
[    0.821262] IRQ: BC_LVL, handler pending
[    0.821267] IRQ: PD tx success
[    0.821740] PD message header: b61 len:0 crc:aaed9191
[    0.821763] gpio_intn_value:1
[    0.821884] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.824770] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.824780] IRQ: BC_LVL, handler pending
[    0.824792] gpio_intn_value:0
[    0.824904] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.825452] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x93, status1: 0x08
[    0.825455] IRQ: BC_LVL, handler pending
[    0.825461] gpio_intn_value:0
[    0.825579] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.826257] IRQ: 0x41, a: 0x00, b: 0x01, status0: 0x93, status1: 0x08
[    0.826267] IRQ: BC_LVL, handler pending
[    0.826272] IRQ: PD sent good CRC
[    0.826919] PD message header: 156f len:4 crc:bbec3cf2
[    0.826948] gpio_intn_value:1
[    0.827069] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.828161] sending PD message header: 1c4f
[    0.828171] sending PD message len: 4
[    0.828765] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x28
[    0.828774] IRQ: BC_LVL, handler pending
[    0.828785] gpio_intn_value:0
[    0.828900] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.829494] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x93, status1: 0x08
[    0.829500] IRQ: BC_LVL, handler pending
[    0.829510] gpio_intn_value:0
[    0.829699] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.830300] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0x93, status1: 0x08
[    0.830312] IRQ: BC_LVL, handler pending
[    0.830317] IRQ: PD tx success
[    0.830754] PD message header: d61 len:0 crc:438e34a4
[    0.830770] gpio_intn_value:1
[    0.830886] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.834132] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.834141] IRQ: BC_LVL, handler pending
[    0.834150] gpio_intn_value:0
[    0.834262] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.834829] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x08
[    0.834836] IRQ: BC_LVL, handler pending
[    0.834844] gpio_intn_value:0
[    0.834960] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.835530] IRQ: 0x41, a: 0x00, b: 0x01, status0: 0x93, status1: 0x08
[    0.835536] IRQ: BC_LVL, handler pending
[    0.835545] IRQ: PD sent good CRC
[    0.836266] PD message header: 276f len:8 crc:cea87bc0
[    0.836284] gpio_intn_value:1
[    0.836400] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.837167] sending PD message header: 2e4f
[    0.837175] sending PD message len: 8
[    0.837802] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x20
[    0.837810] IRQ: BC_LVL, handler pending
[    0.837817] gpio_intn_value:0
[    0.837930] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.838479] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc2, status1: 0x08
[    0.838482] IRQ: BC_LVL, handler pending
[    0.838488] gpio_intn_value:0
[    0.838599] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.839188] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0x93, status1: 0x08
[    0.839194] IRQ: BC_LVL, handler pending
[    0.839199] IRQ: PD tx success
[    0.839624] PD message header: f61 len:0 crc:ad805588
[    0.839643] gpio_intn_value:1
[    0.839760] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.854306] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc2, status1: 0x08
[    0.854316] IRQ: BC_LVL, handler pending
[    0.854326] gpio_intn_value:0
[    0.854435] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.854973] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x08
[    0.854975] IRQ: BC_LVL, handler pending
[    0.854977] gpio_intn_value:0
[    0.855092] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.855659] IRQ: 0x41, a: 0x00, b: 0x01, status0: 0x93, status1: 0x08
[    0.855666] IRQ: BC_LVL, handler pending
[    0.855669] IRQ: PD sent good CRC
[    0.856360] PD message header: 296f len:8 crc:501e30a9
[    0.856378] gpio_intn_value:1
[    0.856548] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.857283] sending PD message header: 104f
[    0.857287] sending PD message len: 4
[    0.857997] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x28
[    0.858012] IRQ: BC_LVL, handler pending
[    0.858023] gpio_intn_value:0
[    0.858143] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.858743] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x93, status1: 0x08
[    0.858752] IRQ: BC_LVL, handler pending
[    0.858758] gpio_intn_value:0
[    0.858867] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.859430] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0x93, status1: 0x08
[    0.859438] IRQ: BC_LVL, handler pending
[    0.859442] IRQ: PD tx success
[    0.859880] PD message header: 161 len:0 crc:4a38788f
[    0.859893] gpio_intn_value:1
[    0.860004] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.880128] sending PD message header: 224f
[    0.880133] sending PD message len: 8
[    0.880826] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x20
[    0.880837] IRQ: BC_LVL, handler pending
[    0.880847] gpio_intn_value:0
[    0.881021] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.881582] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x93, status1: 0x08
[    0.881589] IRQ: BC_LVL, handler pending
[    0.881598] gpio_intn_value:0
[    0.881744] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.882335] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0x93, status1: 0x08
[    0.882343] IRQ: BC_LVL, handler pending
[    0.882346] IRQ: PD tx success
[    0.882786] PD message header: 361 len:0 crc:a43619a3
[    0.882809] gpio_intn_value:1
[    0.882936] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.919277] BC_LVL handler, status0=0x93
