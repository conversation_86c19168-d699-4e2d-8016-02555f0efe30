Jan 25 00:00:00 kernel: klogd started: BusyBox v1.25.0 (2025-07-31 20:04:44 CST)
Jan 25 00:00:00 kernel: [    0.000000] Booting Linux on physical CPU 0x0
Jan 25 00:00:00 kernel: [    0.000000] Linux version 4.9.38 (xreal@082ea61890df) (gcc version 7.5.0 (Linaro GCC 7.5-2019.12) ) #3 SMP Thu Jul 31 20:02:15 CST 2025
Jan 25 00:00:00 kernel: [    0.000000] Boot CPU: AArch64 Processor [410fd034]
Jan 25 00:00:00 kernel: [    0.000000] Memory limited to 304MB
Jan 25 00:00:00 kernel: [    0.000000] Icc memory setup at 0x0000000024000000 size 0x0000000000000800 KB
Jan 25 00:00:00 kernel: [    0.000000] OF: reserved mem: initialized node icc@0x24000000, compatible id icc-region
Jan 25 00:00:00 kernel: [    0.000000] cma: Reserved 16 MiB at 0x0000000032000000
Jan 25 00:00:00 kernel: [    0.000000] On node 0 totalpages: 77822
Jan 25 00:00:00 kernel: [    0.000000]   DMA zone: 1216 pages used for memmap
Jan 25 00:00:00 kernel: [    0.000000]   DMA zone: 0 pages reserved
Jan 25 00:00:00 kernel: [    0.000000]   DMA zone: 77822 pages, LIFO batch:15
Jan 25 00:00:00 kernel: [    0.000000] percpu: Embedded 23 pages/cpu @ffffffc031f61000 s53400 r8192 d32616 u94208
Jan 25 00:00:00 kernel: [    0.000000] pcpu-alloc: s53400 r8192 d32616 u94208 alloc=23*4096
Jan 25 00:00:00 kernel: [    0.000000] pcpu-alloc: [0] 0 [0] 1 [0] 2 [0] 3 
Jan 25 00:00:00 kernel: [    0.000000] Detected VIPT I-cache on CPU0
Jan 25 00:00:00 kernel: [    0.000000] CPU features: enabling workaround for ARM erratum 845719
Jan 25 00:00:00 kernel: [    0.000000] Built 1 zonelists in Zone order, mobility grouping on.  Total pages: 76606
Jan 25 00:00:00 kernel: [    0.000000] Kernel command line: console=ttyS0,disable,earlyprintk loglevel=1,quiet root=/dev/mmcblk0p19 rootwait rw rootfstype=ext4 gpt mem=304m flagfile=/usrdata/sirius-clean-system-flag nmi_watchdog=panic part_info=34952
Jan 25 00:00:00 kernel: [    0.000000] PID hash table entries: 2048 (order: 2, 16384 bytes)
Jan 25 00:00:00 kernel: [    0.000000] Dentry cache hash table entries: 65536 (order: 7, 524288 bytes)
Jan 25 00:00:00 kernel: [    0.000000] Inode-cache hash table entries: 32768 (order: 6, 262144 bytes)
Jan 25 00:00:00 kernel: [    0.000000] Memory: 225680K/311288K available (7550K kernel code, 450K rwdata, 1820K rodata, 2048K init, 359K bss, 69224K reserved, 16384K cma-reserved)
Jan 25 00:00:00 kernel: [    0.000000] Virtual kernel memory layout:
Jan 25 00:00:00 kernel: [    0.000000]     modules : 0xffffff8000000000 - 0xffffff8008000000   (   128 MB)
Jan 25 00:00:00 kernel: [    0.000000]     vmalloc : 0xffffff8008000000 - 0xffffffbebfff0000   (   250 GB)
Jan 25 00:00:00 kernel: [    0.000000]       .text : 0xffffff80080a0000 - 0xffffff8008800000   (  7552 KB)
Jan 25 00:00:00 kernel: [    0.000000]     .rodata : 0xffffff8008800000 - 0xffffff8008a00000   (  2048 KB)
Jan 25 00:00:00 kernel: [    0.000000]       .init : 0xffffff8008a00000 - 0xffffff8008c00000   (  2048 KB)
Jan 25 00:00:00 kernel: [    0.000000]       .data : 0xffffff8008c00000 - 0xffffff8008c70808   (   451 KB)
Jan 25 00:00:00 kernel: [    0.000000]        .bss : 0xffffff8008c70808 - 0xffffff8008cca79c   (   360 KB)
Jan 25 00:00:00 kernel: [    0.000000]     fixed   : 0xffffffbefe7fd000 - 0xffffffbefec00000   (  4108 KB)
Jan 25 00:00:00 kernel: [    0.000000]     PCI I/O : 0xffffffbefee00000 - 0xffffffbeffe00000   (    16 MB)
Jan 25 00:00:00 kernel: [    0.000000]     vmemmap : 0xffffffbf00000000 - 0xffffffc000000000   (     4 GB maximum)
Jan 25 00:00:00 kernel: [    0.000000]               0xffffffbf00800000 - 0xffffffbf00cc0000   (     4 MB actual)
Jan 25 00:00:00 kernel: [    0.000000]     memory  : 0xffffffc020000000 - 0xffffffc033000000   (   304 MB)
Jan 25 00:00:00 kernel: [    0.000000] SLUB: HWalign=64, Order=0-3, MinObjects=0, CPUs=4, Nodes=1
Jan 25 00:00:00 kernel: [    0.000000] Hierarchical RCU implementation.
Jan 25 00:00:00 kernel: [    0.000000] 	Build-time adjustment of leaf fanout to 64.
Jan 25 00:00:00 kernel: [    0.000000] NR_IRQS:64 nr_irqs:64 0
Jan 25 00:00:00 kernel: [    0.000000] arm_arch_timer: Architected cp15 timer(s) running at 24.00MHz (virt).
Jan 25 00:00:00 kernel: [    0.000000] clocksource: arch_sys_counter: mask: 0xffffffffffffff max_cycles: 0x588fe9dc0, max_idle_ns: 440795202592 ns
Jan 25 00:00:00 kernel: [    0.000002] sched_clock: 56 bits at 24MHz, resolution 41ns, wraps every 4398046511097ns
Jan 25 00:00:00 kernel: [    0.000156] Console: colour dummy device 80x25
Jan 25 00:00:00 kernel: [    0.000171] Calibrating delay loop (skipped), value calculated using timer frequency.. 48.00 BogoMIPS (lpj=240000)
Jan 25 00:00:00 kernel: [    0.000175] pid_max: default: 32768 minimum: 301
Jan 25 00:00:00 kernel: [    0.000221] Mount-cache hash table entries: 1024 (order: 1, 8192 bytes)
Jan 25 00:00:00 kernel: [    0.000223] Mountpoint-cache hash table entries: 1024 (order: 1, 8192 bytes)
Jan 25 00:00:00 kernel: [    0.000694] ASID allocator initialised with 65536 entries
Jan 25 00:00:00 kernel: [    0.001664] Detected VIPT I-cache on CPU1
Jan 25 00:00:00 kernel: [    0.001702] CPU1: Booted secondary processor [410fd034]
Jan 25 00:00:00 kernel: [    0.001842] Detected VIPT I-cache on CPU2
Jan 25 00:00:00 kernel: [    0.001854] CPU2: Booted secondary processor [410fd034]
Jan 25 00:00:00 kernel: [    0.001982] Detected VIPT I-cache on CPU3
Jan 25 00:00:00 kernel: [    0.001993] CPU3: Booted secondary processor [410fd034]
Jan 25 00:00:00 kernel: [    0.002014] Brought up 4 CPUs
Jan 25 00:00:00 kernel: [    0.002016] SMP: Total of 4 processors activated.
Jan 25 00:00:00 kernel: [    0.002018] CPU features: detected feature: 32-bit EL0 Support
Jan 25 00:00:00 kernel: [    0.002022] CPU: All CPU(s) started at EL1
Jan 25 00:00:00 kernel: [    0.002031] alternatives: patching kernel code
Jan 25 00:00:00 kernel: [    0.002369] devtmpfs: initialized
Jan 25 00:00:00 kernel: [    0.006438] clocksource: jiffies: mask: 0xffffffff max_cycles: 0xffffffff, max_idle_ns: 19112604462750000 ns
Jan 25 00:00:00 kernel: [    0.006451] futex hash table entries: 1024 (order: 5, 131072 bytes)
Jan 25 00:00:00 kernel: [    0.007517] NET: Registered protocol family 16
Jan 25 00:00:00 kernel: [    0.008078] vdso: 2 pages (1 code @ ffffff8008807000, 1 data @ ffffff8008c04000)
Jan 25 00:00:00 kernel: [    0.008086] hw-breakpoint: found 6 breakpoint and 4 watchpoint registers.
Jan 25 00:00:00 kernel: [    0.008429] DMA: preallocated 256 KiB pool for atomic allocations
Jan 25 00:00:00 kernel: [    0.015871] dw_dmac 1e10000.ahb_dma: DesignWare DMA Controller, 8 channels
Jan 25 00:00:00 kernel: [    0.016423] SCSI subsystem initialized
Jan 25 00:00:00 kernel: [    0.016499] usbcore: registered new interface driver usbfs
Jan 25 00:00:00 kernel: [    0.016519] usbcore: registered new interface driver hub
Jan 25 00:00:00 kernel: [    0.016545] usbcore: registered new device driver usb
Jan 25 00:00:00 kernel: [    0.018053] Linux video capture interface: v2.00
Jan 25 00:00:00 kernel: [    0.018231] icc-artosyn icc-artosyn: assigned reserved memory node icc@0x24000000
Jan 25 00:00:00 kernel: [    0.018592] Advanced Linux Sound Architecture Driver Initialized.
Jan 25 00:00:00 kernel: [    0.018957] clocksource: Switched to clocksource arch_sys_counter
Jan 25 00:00:00 kernel: [    0.020170] NET: Registered protocol family 2
Jan 25 00:00:00 kernel: [    0.020419] TCP established hash table entries: 4096 (order: 3, 32768 bytes)
Jan 25 00:00:00 kernel: [    0.020437] TCP bind hash table entries: 4096 (order: 4, 65536 bytes)
Jan 25 00:00:00 kernel: [    0.020500] TCP: Hash tables configured (established 4096 bind 4096)
Jan 25 00:00:00 kernel: [    0.020531] UDP hash table entries: 256 (order: 1, 8192 bytes)
Jan 25 00:00:00 kernel: [    0.020538] UDP-Lite hash table entries: 256 (order: 1, 8192 bytes)
Jan 25 00:00:00 kernel: [    0.020611] NET: Registered protocol family 1
Jan 25 00:00:00 kernel: [    0.020804] RPC: Registered named UNIX socket transport module.
Jan 25 00:00:00 kernel: [    0.020806] RPC: Registered udp transport module.
Jan 25 00:00:00 kernel: [    0.020807] RPC: Registered tcp transport module.
Jan 25 00:00:00 kernel: [    0.020808] RPC: Registered tcp NFSv4.1 backchannel transport module.
Jan 25 00:00:00 kernel: [    0.021185] hw perfevents: enabled with armv8_cortex_a53 PMU driver, 7 counters available
Jan 25 00:00:00 kernel: [    0.021765] workingset: timestamp_bits=62 max_order=16 bucket_order=0
Jan 25 00:00:00 kernel: [    0.024996] exFAT: file-system version 2.2.0-3arter97
Jan 25 00:00:00 kernel: [    0.025310] NFS: Registering the id_resolver key type
Jan 25 00:00:00 kernel: [    0.025326] Key type id_resolver registered
Jan 25 00:00:00 kernel: [    0.025327] Key type id_legacy registered
Jan 25 00:00:00 kernel: [    0.025333] nfs4filelayout_init: NFSv4 File Layout Driver Registering...
Jan 25 00:00:00 kernel: [    0.025351] fuse init (API version 7.26)
Jan 25 00:00:00 kernel: [    0.026583] io scheduler noop registered
Jan 25 00:00:00 kernel: [    0.026585] io scheduler deadline registered
Jan 25 00:00:00 kernel: [    0.026641] io scheduler cfq registered (default)
Jan 25 00:00:00 kernel: [    0.027040] artosyn_typec_comphy_probe 1288 0 ffffffc030826800
Jan 25 00:00:00 kernel: [    0.027219] artosyn_kuiper_usb2phy_probe 288 0 ffffffc030826c00
Jan 25 00:00:00 kernel: [    0.027282] artosyn_kuiper_usb2phy_probe 288 0 ffffffc030827000
Jan 25 00:00:00 kernel: [    0.028076] gpio-artosyn a10a000.gpio: get 16 irqs for all ports
Jan 25 00:00:00 kernel: [    0.032197] Serial: 8250/16550 driver, 8 ports, IRQ sharing disabled
Jan 25 00:00:00 kernel: [    0.032969] console [ttyS0] disabled
Jan 25 00:00:00 kernel: [    0.032989] 1500000.serial: ttyS0 at MMIO 0x1500000 (irq = 31, base_baud = 8333333) is a 16550A
Jan 25 00:00:00 kernel: [    0.033017] console [ttyS0] enabled
Jan 25 00:00:00 kernel: [    0.033169] 1504000.serial: ttyS2 at MMIO 0x1504000 (irq = 32, base_baud = 8333333) is a 16550A
Jan 25 00:00:00 kernel: [    0.034836] brd: module loaded
Jan 25 00:00:00 kernel: [    0.035356] loop: module loaded
Jan 25 00:00:00 kernel: [    0.035511] artosyn_dprx_probe 3731 -517
Jan 25 00:00:00 kernel: [    0.036194] fast boot device registered
Jan 25 00:00:00 kernel: [    0.036228] hdcp check device registered
Jan 25 00:00:00 kernel: [    0.036275] dphy_reg_driver a09800000000400.dphy_reg: Mapped DPHY0 at 0xa098000 with size 0x400
Jan 25 00:00:00 kernel: [    0.036279] dphy_reg_driver a09800000000400.dphy_reg: Mapped DPHY1 at 0xa098400 with size 0x400
Jan 25 00:00:00 kernel: [    0.036282] dphy_reg_driver a09800000000400.dphy_reg: Mapped DPHY2 at 0xa09c000 with size 0x400
Jan 25 00:00:00 kernel: [    0.036286] dphy_reg_driver a09800000000400.dphy_reg: Mapped DPHY3 at 0xa09c400 with size 0x400
Jan 25 00:00:00 kernel: [    0.036319] dphy_reg_misc_device registered
Jan 25 00:00:00 kernel: [    0.036390] hw_info:hw_id_chan not get! error:-517
Jan 25 00:00:00 kernel: [    0.036403] ia8201_init called
Jan 25 00:00:00 kernel: [    0.036430] ia8201_probe called
Jan 25 00:00:00 kernel: [    0.036454] ret : 0
Jan 25 00:00:00 kernel: [    0.036458] cdev init
Jan 25 00:00:00 kernel: [    0.036459] cdev add
Jan 25 00:00:00 kernel: [    0.036460] create class
Jan 25 00:00:00 kernel: [    0.036470] device create
Jan 25 00:00:00 kernel: [    0.036782] enter dw_spi_add_host
Jan 25 00:00:00 kernel: [    0.036933] spi_master spi0: will run message pump with realtime priority
Jan 25 00:00:00 kernel: [    0.037080] enter dw_spi_add_host
Jan 25 00:00:00 kernel: [    0.037191] spi_master spi1: will run message pump with realtime priority
Jan 25 00:00:00 kernel: [    0.037297] enter dw_spi_add_host
Jan 25 00:00:00 kernel: [    0.037401] spi_master spi2: will run message pump with realtime priority
Jan 25 00:00:00 kernel: [    0.037717] sony_oled_driver_init^M
Jan 25 00:00:00 kernel: [    0.037733] sony_oled_probe-0
Jan 25 00:00:00 kernel: [    0.037737] compatible: sony,ecx348
Jan 25 00:00:00 kernel: [    0.037757] Failed to get oled vin regulator
Jan 25 00:00:00 kernel: [    0.037758] init failed
Jan 25 00:00:00 kernel: [    0.037770] sony_oled_probe-0
Jan 25 00:00:00 kernel: [    0.037771] compatible: sony,ecx348
Jan 25 00:00:00 kernel: [    0.037777] Failed to get oled vin regulator
Jan 25 00:00:00 kernel: [    0.037778] init failed
Jan 25 00:00:00 kernel: [    0.037914] libphy: Fixed MDIO Bus: probed
Jan 25 00:00:00 kernel: [    0.038133] usbcore: registered new interface driver r8152
Jan 25 00:00:00 kernel: [    0.038221] artosyn-dwc3 18.artosyn_dwc3: force disable usb2_using_dwc3
Jan 25 00:00:00 kernel: [    0.038574] 8080000.usb supply vusb_d not found, using dummy regulator
Jan 25 00:00:00 kernel: [    0.038601] 8080000.usb supply vusb_a not found, using dummy regulator
Jan 25 00:00:00 kernel: [    0.038644] artosyn_kuiper_usb2phy_init 105
Jan 25 00:00:00 kernel: [    0.039227] dwc2 8080000.usb: EPs: 16, dedicated fifos, 3968 entries in SPRAM
Jan 25 00:00:00 kernel: [    0.039485] usbcore: registered new interface driver usb-storage
Jan 25 00:00:00 kernel: [    0.039582] hw_info: hw_id_get not cpmplete!!!
Jan 25 00:00:00 kernel: [    0.039584] artosyn_typec_probe hw_id get not complete, re-probe
Jan 25 00:00:00 kernel: [    0.039668] [aw35615] aw35615_probe 1854: aw35615_probe enter
Jan 25 00:00:00 kernel: [    0.039668] hw_info: hw_id_get not cpmplete!!!
Jan 25 00:00:00 kernel: [    0.039670] [aw35615] aw35615_probe 1858: aw35615_probe hw_id get not complete, re-probe
Jan 25 00:00:00 kernel: [    0.039738] i2c /dev entries driver
Jan 25 00:00:00 kernel: [    0.039999] sy_oled_driver_init^M
Jan 25 00:00:00 kernel: [    0.040019] ec_i2c_init 
Jan 25 00:00:00 kernel: [    0.040035] ec major : 0, minor : 0^M
Jan 25 00:00:00 kernel: [    0.040095] ec driver mode:1
Jan 25 00:00:00 kernel: [    0.041245] ec_probe^M
Jan 25 00:00:00 kernel: [    0.041285] usbcore: registered new interface driver uvcvideo
Jan 25 00:00:00 kernel: [    0.041286] USB Video Class driver (1.1.1)
Jan 25 00:00:00 kernel: [    0.041489] dw_wdt 1600000.wdt: using irq(33) mode!
Jan 25 00:00:00 kernel: [    0.042879] sdhci: Secure Digital Host Controller Interface driver
Jan 25 00:00:00 kernel: [    0.042881] sdhci: Copyright(c) Pierre Ossman
Jan 25 00:00:00 kernel: [    0.042882] Synopsys Designware Multimedia Card Interface Driver
Jan 25 00:00:00 kernel: [    0.043030] sdhci-pltfm: SDHCI platform and OF driver helper
Jan 25 00:00:00 kernel: [    0.043209] sdhci-kuiper 8050000.sdhci: Ignore voltage domain gpio.
Jan 25 00:00:00 kernel: [    0.043264] mmc0: Unknown controller version (5). You may experience problems.
Jan 25 00:00:00 kernel: [    0.043576] clk_composite_determine_rate:144, cgu_oscin_clk best_parent_hw 1600000
Jan 25 00:00:00 kernel: [    0.043579] clk_composite_determine_rate:144, cgu_oscin_clk best_parent_hw 1600000
Jan 25 00:00:00 kernel: [    0.043647] mmc0: SDHCI controller on 8050000.sdhci [8050000.sdhci] using ADMA
Jan 25 00:00:00 kernel: [    0.043893] hidraw: raw HID events driver (C) Jiri Kosina
Jan 25 00:00:00 kernel: [    0.043949] usbcore: registered new interface driver usbhid
Jan 25 00:00:00 kernel: [    0.043950] usbhid: USB HID core driver
Jan 25 00:00:00 kernel: [    0.044103] ashmem: initialized
Jan 25 00:00:00 kernel: [    0.044571] spi0.0 supply inven,vdd_ana not found, using dummy regulator
Jan 25 00:00:00 kernel: [    0.044591] spi0.0 supply inven,vcc_i2c not found, using dummy regulator
Jan 25 00:00:00 kernel: [    0.044626] inv_mpu: inv_mpu_probe: power on here.
Jan 25 00:00:00 kernel: [    0.044627] inv_mpu: inv_mpu_probe: power on.
Jan 25 00:00:00 kernel: [    0.088851] clk_composite_determine_rate:144, cgu_oscin_clk best_parent_hw 24000000
Jan 25 00:00:00 kernel: [    0.088892] clk_composite_determine_rate:144, fix_pll_600 best_parent_hw 600000000
Jan 25 00:00:00 kernel: [    0.088900] clk_composite_determine_rate:144, cgu_oscin_clk best_parent_hw 24000000
Jan 25 00:00:00 kernel: [    0.088902] clk_composite_determine_rate:144, fix_pll_600 best_parent_hw 600000000
Jan 25 00:00:00 kernel: [    0.089028] sdhci-kuiper 8050000.sdhci: mmc0: Found good phase = 0
Jan 25 00:00:00 kernel: [    0.089086] sdhci-kuiper 8050000.sdhci: mmc0: Found good phase = 1
Jan 25 00:00:00 kernel: [    0.089139] sdhci-kuiper 8050000.sdhci: mmc0: Found good phase = 2
Jan 25 00:00:00 kernel: [    0.089194] sdhci-kuiper 8050000.sdhci: mmc0: Found good phase = 3
Jan 25 00:00:00 kernel: [    0.089250] sdhci-kuiper 8050000.sdhci: mmc0: Found good phase = 4
Jan 25 00:00:00 kernel: [    0.089305] sdhci-kuiper 8050000.sdhci: mmc0: Found good phase = 5
Jan 25 00:00:00 kernel: [    0.089408] sdhci-kuiper 8050000.sdhci: mmc0: Found good phase = 7
Jan 25 00:00:00 kernel: [    0.089434] sdhci-kuiper 8050000.sdhci: mmc0: Setting the tuning phase to 2
Jan 25 00:00:00 kernel: [    0.089481] mmc0: new HS200 MMC card at address 0001
Jan 25 00:00:00 kernel: [    0.089666] mmcblk0: mmc0:0001 58A421 3.65 GiB 
Jan 25 00:00:00 kernel: [    0.089732] mmcblk0boot0: mmc0:0001 58A421 partition 1 4.00 MiB
Jan 25 00:00:00 kernel: [    0.089795] mmcblk0boot1: mmc0:0001 58A421 partition 2 4.00 MiB
Jan 25 00:00:00 kernel: [    0.089855] mmcblk0rpmb: mmc0:0001 58A421 partition 3 16.0 MiB
Jan 25 00:00:00 kernel: [    0.091198] Alternate GPT is invalid, using primary GPT.
Jan 25 00:00:00 kernel: [    0.091227]  mmcblk0: p1 p2 p3 p4 p5 p6 p7 p8 p9 p10 p11 p12 p13 p14 p15 p16 p17 p18 p19 p20 p21 p22
Jan 25 00:00:00 kernel: [    0.159009] inv_mpu: whoami= 0
Jan 25 00:00:00 kernel: [    0.159025] inv_mpu: whoami= 0
Jan 25 00:00:00 kernel: [    0.159036] inv_mpu: whoami= 0
Jan 25 00:00:00 kernel: [    0.159056] inv-mpu-iio-spi spi0.0: inv_mpu_probe failed -19
Jan 25 00:00:00 kernel: [    0.159133] spi1.0 supply inven,vdd_ana not found, using dummy regulator
Jan 25 00:00:00 kernel: [    0.159155] spi1.0 supply inven,vcc_i2c not found, using dummy regulator
Jan 25 00:00:00 kernel: [    0.159200] inv_mpu: inv_mpu_probe: power on here.
Jan 25 00:00:00 kernel: [    0.159201] inv_mpu: inv_mpu_probe: power on.
Jan 25 00:00:00 kernel: [    0.278979] inv_mpu: whoami= e9
Jan 25 00:00:00 kernel: [    0.309796] inv_mpu: data_new=0 40, 4, 40,8
Jan 25 00:00:00 kernel: [    0.309879] inv_mpu: external clkin enable
Jan 25 00:00:00 kernel: [    0.309902] inv_mpu: INT2 pin mux set to CLKIN ret = 0
Jan 25 00:00:00 kernel: [    0.309926] inv_mpu: RTC MODE enable ret = 0
Jan 25 00:00:00 kernel: [    0.310059] inv_mpu: I3C STC MODE disable ret = 0
Jan 25 00:00:00 kernel: [    0.310191] inv_mpu: accel source set ret = 0
Jan 25 00:00:00 kernel: [    0.310271] random: fast init done
Jan 25 00:00:00 kernel: [    0.310326] inv_mpu: gyro source set ret = 0
Jan 25 00:00:00 kernel: [    0.310349] inv_mpu: AUX2 disable ret = 0
Jan 25 00:00:00 kernel: [    0.310350] inv_mpu: external clkin enable result is 0
Jan 25 00:00:00 kernel: [    0.318166] inv_mpu: write mag matrix data
Jan 25 00:00:00 kernel: [    0.321794] inv_mpu: inv_mpu_initialize: initialize result is 0....
Jan 25 00:00:00 kernel: [    0.322100] inv_mpu: wakeup_source is created successfully
Jan 25 00:00:00 kernel: [    0.322103] inv-mpu-iio-spi spi1.0: icm45600 ma-kernel-10.2.4 is ready to go!
Jan 25 00:00:00 kernel: [    0.322105] inv-mpu-iio-spi spi1.0: inv-mpu-iio clock type 1
Jan 25 00:00:00 kernel: [    0.322106] inv_mpu: Data read from FIFO
Jan 25 00:00:00 kernel: [    0.322178] mmc5603 5-0030: enter mmc5603_probe
Jan 25 00:00:00 kernel: [    0.322491] mmc5603 5-0030: MMC5603 chip id 10
Jan 25 00:00:00 kernel: [    0.339193] NET: Registered protocol family 10
Jan 25 00:00:00 kernel: [    0.339600] sit: IPv6, IPv4 and MPLS over IPv4 tunneling driver
Jan 25 00:00:00 kernel: [    0.339843] NET: Registered protocol family 17
Jan 25 00:00:00 kernel: [    0.339888] Key type dns_resolver registered
Jan 25 00:00:00 kernel: [    0.343608] artosyn_dprx_probe 3731 -517
Jan 25 00:00:00 kernel: [    0.343740] hw_info: hw_id_chan get!
Jan 25 00:00:00 kernel: [    0.348122] hw_info:read hw_id raw :2143
Jan 25 00:00:00 kernel: [    0.348203] Misc device registered: hw_info, minor number = 1013
Jan 25 00:00:00 kernel: [    0.348229] sony_oled_probe-0
Jan 25 00:00:00 kernel: [    0.348231] compatible: sony,ecx348
Jan 25 00:00:00 kernel: [    0.348275] of_get_named_gpio = 77
Jan 25 00:00:00 kernel: [    0.348277] gpio gpio-rst-77 request ok
Jan 25 00:00:00 kernel: [    0.348279] dev->rst_gpio77
Jan 25 00:00:00 kernel: [    0.358280] find label: ecx348_left
Jan 25 00:00:00 kernel: [    0.358377] check spi communication ok!
Jan 25 00:00:00 kernel: [    0.358381] major : 246, minor : 0
Jan 25 00:00:00 kernel: [    0.358385] cdev add successfully
Jan 25 00:00:00 kernel: [    0.358396] class create successfully
Jan 25 00:00:00 kernel: [    0.358449] device create successfully
Jan 25 00:00:00 kernel: [    0.358470] sony_oled_probe-1
Jan 25 00:00:00 kernel: [    0.358471] compatible: sony,ecx348
Jan 25 00:00:00 kernel: [    0.358492] of_get_named_gpio = 28
Jan 25 00:00:00 kernel: [    0.358494] gpio gpio-rst-28 request ok
Jan 25 00:00:00 kernel: [    0.358495] dev->rst_gpio28
Jan 25 00:00:00 kernel: [    0.368497] find label: ecx348_right
Jan 25 00:00:00 kernel: [    0.368571] check spi communication ok!
Jan 25 00:00:00 kernel: [    0.368572] major : 246, minor : 1
Jan 25 00:00:00 kernel: [    0.368573] cdev add successfully
Jan 25 00:00:00 kernel: [    0.368582] class create successfully
Jan 25 00:00:00 kernel: [    0.368621] device create successfully
Jan 25 00:00:00 kernel: [    0.368674] hw_info: hw_id : 9
Jan 25 00:00:00 kernel: [    0.368675] artosyn_typec_probe hw_id:9,pd disable
Jan 25 00:00:00 kernel: [    0.368752] artosyn_tcpc soc_internal_pd_disabled: 1
Jan 25 00:00:00 kernel: [    0.368825] artosyn_tcpc typec_base: ffffff8008f9a000, pd_base: ffffff8008fb2000
Jan 25 00:00:00 kernel: [    0.368827] artosyn_typec_probe 1860 success! ffffffc0312a8c10 ffffffc030826400 ffffffc030b8d5c0
Jan 25 00:00:00 kernel: [    0.368852] [aw35615] aw35615_probe 1854: aw35615_probe enter
Jan 25 00:00:00 kernel: [    0.368853] hw_info: hw_id : 9
Jan 25 00:00:00 kernel: [    0.368854] [aw35615] aw35615_probe 1866: aw35615_probe hw_id:9,aw ic exsit, continue
Jan 25 00:00:00 kernel: [    0.368986] tcpc_aw35615 2-0022: vid is correct, 0x91
Jan 25 00:00:00 kernel: [    0.368996] 2-0022 supply vbus not found, using dummy regulator
Jan 25 00:00:00 kernel: [    0.369091] xreal-PD Driver: aw35615_debugfs_init debugfs :aw35615-2-0022
Jan 25 00:00:00 kernel: [    0.369731] OF: graph: no port node found in /soc/i2c2@01204000/aw35615@22/connector
Jan 25 00:00:00 kernel: [    0.369891] OF: graph: no port node found in /soc/i2c2@01204000/aw35615@22/connector
Jan 25 00:00:00 kernel: [    0.369903] OF: graph: no port node found in /soc/i2c2@01204000/aw35615@22/connector
Jan 25 00:00:00 kernel: [    0.369953] connector altmodes
Jan 25 00:00:00 kernel: [    0.369954] altmodes altmodes
Jan 25 00:00:00 kernel: [    0.369958] OF: graph: no port node found in /soc/i2c2@01204000/aw35615@22/connector
Jan 25 00:00:00 kernel: [    0.369965] OF: graph: no port node found in /soc/i2c2@01204000/aw35615@22/connector
Jan 25 00:00:00 kernel: [    0.372628] artosyn_set_orientation 768 0
Jan 25 00:00:00 kernel: [    0.372636] artosyn_set_mux 799 mode 0 polarity 0
Jan 25 00:00:00 kernel: [    0.372639] artosyn-tcpc a124000.tcpc: dp altmode not register
Jan 25 00:00:00 kernel: [    0.372916] [aw35615] aw35615_probe 1977: probe ok
Jan 25 00:00:00 kernel: [    0.372999] artosyn_typec_reset_assert 1517 5
Jan 25 00:00:00 kernel: [    0.373249] dprx-artosyn a0a0000.dp: edid form ddr : magic mismatch
Jan 25 00:00:00 kernel: [    0.373255] dprx-artosyn a0a0000.dp: no valid edid in mmc, try to get edid from dts
Jan 25 00:00:00 kernel: [    0.373413] try load hdcp ret : 0, hdcp_key_length : 685
Jan 25 00:00:00 kernel: [    0.373416] dprx-artosyn a0a0000.dp: hdcp firmware load.
Jan 25 00:00:00 kernel: [    0.373626] artosyn_typec_event 1631 0!
Jan 25 00:00:00 kernel: [    0.373648] artosyn_dprx_probe 3949 success ffffffc030826400           (null)
Jan 25 00:00:00 kernel: [    0.373949] input: gpio-keys as /devices/platform/gpio-keys/input/input0
Jan 25 00:00:00 kernel: [    0.374007] artosyn_dprx_dp_pd_event_work 2879 0
Jan 25 00:00:00 kernel: [    0.374022] artosyn_set_orientation 768 0
Jan 25 00:00:00 kernel: [    0.374035] artosyn_set_mux 799 mode 0 polarity 0
Jan 25 00:00:00 kernel: [    0.374038] artosyn-tcpc a124000.tcpc: dp altmode not register
Jan 25 00:00:00 kernel: [    0.374081] hctosys: unable to open rtc device (rtc0)
Jan 25 00:00:00 kernel: [    0.374211] vmmc sdmmc1: disabling
Jan 25 00:00:00 kernel: [    0.374214] vqmmc sdmmc0: disabling
Jan 25 00:00:00 kernel: [    0.374216] vqmmc sdmmc1: disabling
Jan 25 00:00:00 kernel: [    0.374218] vqmmc sdhci: disabling
Jan 25 00:00:00 kernel: [    0.374220] eta355t: disabling
Jan 25 00:00:00 kernel: [    0.374223] eta355t: disabling
Jan 25 00:00:00 kernel: [    0.374225] eta355t: disabling
Jan 25 00:00:00 kernel: [    0.374228] ALSA device list:
Jan 25 00:00:00 kernel: [    0.374229]   No soundcards found.
Jan 25 00:00:00 kernel: [    0.427753] EXT4-fs (mmcblk0p19): recovery complete
Jan 25 00:00:00 kernel: [    0.427869] EXT4-fs (mmcblk0p19): mounted filesystem with ordered data mode. Opts: (null)
Jan 25 00:00:00 kernel: [    0.427903] VFS: Mounted root (ext4 filesystem) on device 259:11.
Jan 25 00:00:00 kernel: [    0.428525] Freeing unused kernel memory: 2048K (ffffffc020a00000 - ffffffc020c00000)
Jan 25 00:00:00 kernel: [    0.446719] EXT4-fs (mmcblk0p19): re-mounted. Opts: data=ordered
Jan 25 00:00:00 kernel: [    0.523990] EXT4-fs (mmcblk0p5): mounted filesystem with ordered data mode. Opts: (null)
Jan 25 00:00:00 kernel: [    0.583561] artosyn_set_orientation 768 1
Jan 25 00:00:00 kernel: [    0.583604] artosyn_set_mux 799 mode 1 polarity 0
Jan 25 00:00:00 kernel: [    0.583613] artosyn-tcpc a124000.tcpc: dp altmode not register
Jan 25 00:00:00 kernel: [    0.585608] EXT4-fs (mmcblk0p3): mounted filesystem with ordered data mode. Opts: (null)
Jan 25 00:00:00 kernel: [    0.607654] EXT4-fs (mmcblk0p21): mounted filesystem with ordered data mode. Opts: (null)
Jan 25 00:00:00 kernel: [    0.694959] Adding 262140k swap on /usrdata/swapfile.  Priority:-1 extents:3 across:278524k SS
Jan 25 00:00:00 kernel: [    0.703364] ar_mpp_drv: loading out-of-tree module taints kernel.
Jan 25 00:00:00 kernel: [    0.726272] Module osal: init ok
Jan 25 00:00:00 kernel: [    0.726278] Media Memory Zone Manager
Jan 25 00:00:00 kernel: [    0.727700] osal 1.0 init success!
Jan 25 00:00:00 kernel: [    0.746135] ar_vb_init 0
Jan 25 00:00:00 kernel: [    0.748466] ar_sys_init 0
Jan 25 00:00:00 kernel: [    0.752725] camera_pwr_ioctl_init
Jan 25 00:00:00 kernel: [    0.752912] camera_pwr_ioctl_probe
Jan 25 00:00:00 kernel: [    0.752957] get camera_pwr success
Jan 25 00:00:00 kernel: [    0.753113] get camera_rst success
Jan 25 00:00:00 kernel: [    0.753198] cv0_pwr gpio is not set!
Jan 25 00:00:00 kernel: [    0.753203] cv1_pwr gpio is not set!
Jan 25 00:00:00 kernel: [    0.753204] cv0_rst gpio is not set!
Jan 25 00:00:00 kernel: [    0.753207] cv1_rst gpio is not set!
Jan 25 00:00:00 kernel: [    0.753221] get cam_led success
Jan 25 00:00:00 kernel: [    0.753314] camera_pwr_ioctl_probe complete.
Jan 25 00:00:00 kernel: [    0.756058]  camera timestamp_record_exp_init
Jan 25 00:00:00 kernel: [    0.756244]  ts_driver_probe
Jan 25 00:00:00 kernel: [    0.756287] get camera timestamp gpio[27] success
Jan 25 00:00:00 kernel: [    0.756291] irq for gpio[27],irq[0]
Jan 25 00:00:00 kernel: [    0.758406]  imx681 camera_plug_detect_record_init
Jan 25 00:00:00 kernel: [    0.758598]  Camera_plug_detect driver probe
Jan 25 00:00:00 kernel: [    0.758644] get Camera plug detect gpio[13] success
Jan 25 00:00:00 kernel: [    0.758647] Camera plug detect irq for gpio[13],irq[69]
Jan 25 00:00:00 kernel: [    0.759605] camera plug_in_out_event_process_thread is running
Jan 25 00:00:00 kernel: [    0.760850] VO_PACK Init Start1...
Jan 25 00:00:00 kernel: [    0.761016] VO_PACK probe start.
Jan 25 00:00:00 kernel: [    0.761046] dev_0 start:8820000 size:100000 base: ffffff8009200000.
Jan 25 00:00:00 kernel: [    0.761089] dev0 irq:44 .
Jan 25 00:00:00 kernel: [    0.761101] dev_1 start:8840000 size:100000 base: ffffff8009400000.
Jan 25 00:00:00 kernel: [    0.761106] dev1 irq:45 .
Jan 25 00:00:00 kernel: [    0.761223] VO_PACK probe end.
Jan 25 00:00:00 kernel: [    0.827230] typec_displayport_sink port0-partner.0: dp_sink_altmode_probe 552 ffffffc030826400
Jan 25 00:00:00 kernel: [    0.827261] dp_sink_altmode_vdm 318 cmdt 0 cmd 4 hdr ff018104
Jan 25 00:00:00 kernel: [    0.827263] dp_sink_altmode_vdm 320 vdo 30b81680
Jan 25 00:00:00 kernel: [    0.827284] dp_sink_altmode_vdm 375 state 1
Jan 25 00:00:00 kernel: [    0.829046] iqs323_init
Jan 25 00:00:00 kernel: [    0.829269] iqs323 3-0044: gpio gpio-rdy-57 request ok
Jan 25 00:00:00 kernel: [    0.836320] dp_sink_altmode_vdm 318 cmdt 0 cmd 16 hdr ff018110
Jan 25 00:00:00 kernel: [    0.836326] dp_sink_altmode_vdm 320 vdo 1
Jan 25 00:00:00 kernel: [    0.836335] dp_sink_altmode_vdm 375 state 2
Jan 25 00:00:00 kernel: [    0.856411] dp_sink_altmode_vdm 318 cmdt 0 cmd 17 hdr ff018111
Jan 25 00:00:00 kernel: [    0.856417] dp_sink_altmode_vdm 320 vdo 406
Jan 25 00:00:00 kernel: [    0.856428] dp_sink_altmode_vdm 375 state 3
Jan 25 00:00:00 kernel: [    0.856444] artosyn_set_mux 799 mode 0 polarity 0
Jan 25 00:00:00 kernel: [    0.856460] dp_sink_altmode_configure_vdm 191 0x406
Jan 25 00:00:00 kernel: [    0.856466] dp_sink_altmode_notify 106 0x4 2 4
Jan 25 00:00:00 kernel: [    0.856468] artosyn_set_mux 799 mode 4 polarity 0
Jan 25 00:00:00 kernel: [    0.856493] artosyn_typec_event 1631 1!
Jan 25 00:00:00 kernel: [    0.856504] dp_sink_altmode_event 485 1 dp->hpd:0 hpd:0 irq:0
Jan 25 00:00:00 kernel: [    0.856576] artosyn_dprx_dp_pd_event_work 2879 1
Jan 25 00:00:00 kernel: [    0.856583] dp random: 71e10e0f0283d6e2
Jan 25 00:00:00 kernel: [    0.856610] artosyn_dprx_init_phy 813 get lane info 4 4 0!
Jan 25 00:00:00 kernel: [    0.856615] artosyn_typec_comphy_init 627
Jan 25 00:00:00 kernel: [    0.856697] artosyn_typec_comphy_set_mode 713 dp mode
Jan 25 00:00:00 kernel: [    0.856778] artosyn_typec_reset_deassert 1546 5
Jan 25 00:00:00 kernel: [    0.879221] artosyn_dprx_init_ctrl 1225 success
Jan 25 00:00:00 kernel: [    0.879233] artosyn_typec_event 1631 1!
Jan 25 00:00:00 kernel: [    0.879245] dp_sink_altmode_event 485 1 dp->hpd:0 hpd:1 irq:0
Jan 25 00:00:00 kernel: [    0.879252] dp_sink_altmode_attention_vdm 300
Jan 25 00:00:00 kernel: [    0.879307] state change 0 -> 1
Jan 25 00:00:00 kernel: [    0.879312] artosyn_dprx_dp_pd_event_work 2879 1
Jan 25 00:00:00 kernel: [    0.889100] iqs323 3-0044: gpio_num_57: using irq 113 for Cap Sensor rdy signal detection
Jan 25 00:00:00 kernel: [    0.889108] iqs323 3-0044: iqs323_reset is ready! 
Jan 25 00:00:00 kernel: [    0.889434] iqs323 3-0044: iqs323_probe is over : 0
Jan 25 00:00:00 kernel: [    0.909723] ar_vb_open
Jan 25 00:00:00 kernel: [    0.909746] VB_EXITMCPL: not is_inited yet
Jan 25 00:00:00 kernel: [    0.909770] smartPA_init 
Jan 25 00:00:00 kernel: [    0.909836] ar_vb_release
Jan 25 00:00:00 kernel: [    0.909857] smartPA_probe slave addr 52
Jan 25 00:00:00 kernel: [    0.909867] ar_vb_open
Jan 25 00:00:00 kernel: [    0.909900] Create 0 common pools
Jan 25 00:00:00 kernel: [    0.909927] of_get_named_gpio = 79
Jan 25 00:00:00 kernel: [    0.909935] find label: HIGH
Jan 25 00:00:00 kernel: [    0.909939] gpio_direction_output: HIGH
Jan 25 00:00:00 kernel: [    0.909941] find label: smartPA_L
Jan 25 00:00:00 kernel: [    0.951908] hil_mmb_alloc pa:0x33284000 len:204800!
Jan 25 00:00:00 kernel: [    0.952253] hil_mmb_alloc pa:0x332b6000 len:4096!
Jan 25 00:00:00 kernel: [    0.952300] hil_mmb_alloc pa:0x332b7000 len:53248000!
Jan 25 00:00:00 kernel: [    0.952326] hil_mmb_alloc pa:0x3657f000 len:2768896!
Jan 25 00:00:00 kernel: [    0.952354] hil_mmb_alloc pa:0x36823000 len:66600960!
Jan 25 00:00:00 kernel: [    0.952378] hil_mmb_alloc pa:0x3a7a7000 len:8847360!
Jan 25 00:00:00 kernel: [    0.952403] hil_mmb_alloc pa:0x3b017000 len:25677824!
Jan 25 00:00:00 kernel: [    0.952425] hil_mmb_alloc pa:0x3c894000 len:81920!
Jan 25 00:00:00 kernel: [    0.970485] no support addr 0x303c 2 2
Jan 25 00:00:00 kernel: [    0.970825] no support addr 0xb0 2 2
Jan 25 00:00:00 kernel: [    0.972088] 6921d 2 3
Jan 25 00:00:00 kernel: [    0.982107] iqs323 3-0044: iqs323_irq_work:product_number = 1106 
Jan 25 00:00:00 kernel: [    0.982804] dprx-artosyn a0a0000.dp: rd 0x0
Jan 25 00:00:00 kernel: [    0.982806] dprx-artosyn a0a0000.dp: IC_INTR_R_RX_FULL 1 0
Jan 25 00:00:00 kernel: [    0.983126] dprx-artosyn a0a0000.dp: IC_INTR_R_RD_REQ 0
Jan 25 00:00:00 kernel: [    0.983130] dprx-artosyn a0a0000.dp: wr 0 0 0 16
Jan 25 00:00:00 kernel: [    0.983537] dprx-artosyn a0a0000.dp: IC_INTR_R_STOP_DET
Jan 25 00:00:00 kernel: [    0.985000] dprx-artosyn a0a0000.dp: rd 0x0
Jan 25 00:00:00 kernel: [    0.985006] dprx-artosyn a0a0000.dp: IC_INTR_R_RX_FULL 1 0
Jan 25 00:00:00 kernel: [    0.985310] dprx-artosyn a0a0000.dp: IC_INTR_R_RD_REQ 0
Jan 25 00:00:00 kernel: [    0.985316] dprx-artosyn a0a0000.dp: wr 0 0 0 16
Jan 25 00:00:00 kernel: [    0.985454] iqs323 3-0044: iqs323_irq_work:major_version = 1 
Jan 25 00:00:00 kernel: [    0.986086] dprx-artosyn a0a0000.dp: IC_INTR_R_RD_REQ 0
Jan 25 00:00:00 kernel: [    0.986091] dprx-artosyn a0a0000.dp: wr 0 0 16 16
Jan 25 00:00:00 kernel: [    0.986869] dprx-artosyn a0a0000.dp: IC_INTR_R_RD_REQ 0
Jan 25 00:00:00 kernel: [    0.986872] dprx-artosyn a0a0000.dp: wr 0 0 32 16
Jan 25 00:00:00 kernel: [    0.987675] dprx-artosyn a0a0000.dp: IC_INTR_R_RD_REQ 0
Jan 25 00:00:00 kernel: [    0.987679] dprx-artosyn a0a0000.dp: wr 0 0 48 16
Jan 25 00:00:00 kernel: [    0.988465] dprx-artosyn a0a0000.dp: IC_INTR_R_RD_REQ 0
Jan 25 00:00:00 kernel: [    0.988469] dprx-artosyn a0a0000.dp: wr 0 0 64 16
Jan 25 00:00:00 kernel: [    0.988776] iqs323 3-0044: iqs323_irq_work:minor_version = 3 
Jan 25 00:00:00 kernel: [    0.989262] dprx-artosyn a0a0000.dp: IC_INTR_R_RD_REQ 0
Jan 25 00:00:00 kernel: [    0.989267] dprx-artosyn a0a0000.dp: wr 0 0 80 16
Jan 25 00:00:00 kernel: [    0.990057] dprx-artosyn a0a0000.dp: IC_INTR_R_RD_REQ 0
Jan 25 00:00:00 kernel: [    0.990059] dprx-artosyn a0a0000.dp: wr 0 0 96 16
Jan 25 00:00:00 kernel: [    0.990858] dprx-artosyn a0a0000.dp: IC_INTR_R_RD_REQ 0
Jan 25 00:00:00 kernel: [    0.990861] dprx-artosyn a0a0000.dp: wr 0 0 112 16
Jan 25 00:00:00 kernel: [    0.991645] dprx-artosyn a0a0000.dp: IC_INTR_R_STOP_DET
Jan 25 00:00:00 kernel: [    0.992097] iqs323 3-0044: iqs323_irq_work:iqs323 has been reset
Jan 25 00:00:00 kernel: [    0.993004] dprx-artosyn a0a0000.dp: IC_INTR_R_STOP_DET
Jan 25 00:00:00 kernel: [    0.993201] dprx-artosyn a0a0000.dp: DP i2c reset 1
Jan 25 00:00:00 kernel: [    0.993875] dprx-artosyn a0a0000.dp: IC_INTR_R_STOP_DET
Jan 25 00:00:00 kernel: [    0.994080] dprx-artosyn a0a0000.dp: DP i2c reset 1
Jan 25 00:00:00 kernel: [    0.994730] dprx-artosyn a0a0000.dp: IC_INTR_R_STOP_DET
Jan 25 00:00:00 kernel: [    0.994905] dprx-artosyn a0a0000.dp: DP i2c reset 1
Jan 25 00:00:00 kernel: [    0.995574] dprx-artosyn a0a0000.dp: IC_INTR_R_STOP_DET
Jan 25 00:00:00 kernel: [    0.995769] dprx-artosyn a0a0000.dp: DP i2c reset 1
Jan 25 00:00:00 kernel: [    0.996423] dprx-artosyn a0a0000.dp: IC_INTR_R_STOP_DET
Jan 25 00:00:00 kernel: [    0.996599] dprx-artosyn a0a0000.dp: DP i2c reset 1
Jan 25 00:00:00 kernel: [    0.997261] dprx-artosyn a0a0000.dp: IC_INTR_R_STOP_DET
Jan 25 00:00:00 kernel: [    0.997443] dprx-artosyn a0a0000.dp: DP i2c reset 1
Jan 25 00:00:00 kernel: [    0.998059] dprx-artosyn a0a0000.dp: IC_INTR_R_STOP_DET
Jan 25 00:00:00 kernel: [    0.998220] dprx-artosyn a0a0000.dp: DP i2c reset 1
Jan 25 00:00:00 kernel: [    0.998927] dprx-artosyn a0a0000.dp: IC_INTR_R_STOP_DET
Jan 25 00:00:00 kernel: [    0.999173] dprx-artosyn a0a0000.dp: DP i2c reset 1
Jan 25 00:00:00 kernel: [    1.000916] dprx-artosyn a0a0000.dp: IC_INTR_R_STOP_DET
Jan 25 00:00:00 kernel: [    1.001103] dprx-artosyn a0a0000.dp: DP i2c reset 1
Jan 25 00:00:00 kernel: [    1.001768] dprx-artosyn a0a0000.dp: IC_INTR_R_STOP_DET
Jan 25 00:00:00 kernel: [    1.001941] dprx-artosyn a0a0000.dp: DP i2c reset 1
Jan 25 00:00:00 kernel: [    1.002609] dprx-artosyn a0a0000.dp: IC_INTR_R_STOP_DET
Jan 25 00:00:00 kernel: [    1.002802] dprx-artosyn a0a0000.dp: DP i2c reset 1
Jan 25 00:00:00 kernel: [    1.003450] dprx-artosyn a0a0000.dp: IC_INTR_R_STOP_DET
Jan 25 00:00:00 kernel: [    1.003629] dprx-artosyn a0a0000.dp: DP i2c reset 1
Jan 25 00:00:00 kernel: [    1.006515] power up done 1
Jan 25 00:00:00 kernel: [    1.015280] state change 1 -> 2
Jan 25 00:00:00 kernel: [    1.015296] pending state change 2 -> 3 @ 1000 ms
Jan 25 00:00:00 kernel: [    1.015305] link rate change 10000
Jan 25 00:00:00 kernel: [    1.015307] PHY 0x744: 068fc180
Jan 25 00:00:00 kernel: [    1.015308] 0x2208: 000000ff
Jan 25 00:00:00 kernel: [    1.015309] 0x2400: 00000000
Jan 25 00:00:00 kernel: [    1.015311] 0x2404: 00000000
Jan 25 00:00:00 kernel: [    1.015312] 0x2408: 00000000
Jan 25 00:00:00 kernel: [    1.015313] 0x240c: 00000000
Jan 25 00:00:00 kernel: [    1.015315] 0x1104: 00000000
Jan 25 00:00:00 kernel: [    1.015316] 0x1108: 010f0000
Jan 25 00:00:00 kernel: [    1.015317] 0x1118: 00000000
Jan 25 00:00:00 kernel: [    1.015318] 0x1134: 01010000
Jan 25 00:00:00 kernel: [    1.015320] 0x1138: 00000000
Jan 25 00:00:00 kernel: [    1.015321] 0x113c: 00000000
Jan 25 00:00:00 kernel: [    1.015322] 0x1140: 8003f000
Jan 25 00:00:00 kernel: [    1.015323] PHY 0x3d8: 00000008
Jan 25 00:00:00 kernel: [    1.015324] PHY 0x43d8: 00000008
Jan 25 00:00:00 kernel: [    1.015326] PHY 0x83d8: 00000008
Jan 25 00:00:00 kernel: [    1.015327] PHY 0xc3d8: 00000008
Jan 25 00:00:00 kernel: [    1.015328] PHY 0x1414: 40400040
Jan 25 00:00:00 kernel: [    1.015329] PHY 0x44: 00000000
Jan 25 00:00:00 kernel: [    1.015330] PHY 0x20: 00006080
Jan 25 00:00:00 kernel: [    1.015331] PHY 0x73c: 0000081f
Jan 25 00:00:00 kernel: [    1.015332] PHY 0x14: 60692540
Jan 25 00:00:00 kernel: [    1.015336] link count change 20000
Jan 25 00:00:00 kernel: [    1.015338] PHY 0x744: 068fc180
Jan 25 00:00:00 kernel: [    1.015339] 0x2208: 000000ff
Jan 25 00:00:00 kernel: [    1.015340] 0x2400: 00000000
Jan 25 00:00:00 kernel: [    1.015341] 0x2404: 00000000
Jan 25 00:00:00 kernel: [    1.015342] 0x2408: 00000000
Jan 25 00:00:00 kernel: [    1.015343] 0x240c: 00000000
Jan 25 00:00:00 kernel: [    1.015344] 0x1104: 00000000
Jan 25 00:00:00 kernel: [    1.015345] 0x1108: 010f0000
Jan 25 00:00:00 kernel: [    1.015347] 0x1118: 00000000
Jan 25 00:00:00 kernel: [    1.015348] 0x1134: 01010000
Jan 25 00:00:00 kernel: [    1.015349] 0x1138: 00000000
Jan 25 00:00:00 kernel: [    1.015350] 0x113c: 00000000
Jan 25 00:00:00 kernel: [    1.015351] 0x1140: 8003f000
Jan 25 00:00:00 kernel: [    1.015352] PHY 0x3d8: 00000008
Jan 25 00:00:00 kernel: [    1.015353] PHY 0x43d8: 00000008
Jan 25 00:00:00 kernel: [    1.015354] PHY 0x83d8: 00000008
Jan 25 00:00:00 kernel: [    1.015356] PHY 0xc3d8: 00000008
Jan 25 00:00:00 kernel: [    1.015357] PHY 0x1414: 40400040
Jan 25 00:00:00 kernel: [    1.015358] PHY 0x44: 00000000
Jan 25 00:00:00 kernel: [    1.015359] PHY 0x20: 00006080
Jan 25 00:00:00 kernel: [    1.015360] PHY 0x73c: 0000081f
Jan 25 00:00:00 kernel: [    1.015362] PHY 0x14: 60692540
Jan 25 00:00:00 kernel: [    1.015502] state change 2 -> 3
Jan 25 00:00:00 kernel: [    1.015506] pending state change 3 -> 4 @ 1000 ms
Jan 25 00:00:00 kernel: [    1.015510] training pattern change 1000,rate a,cnt 1, pattern 1
Jan 25 00:00:00 kernel: [    1.015553] artosyn_typec_reset_assert 1517 5
Jan 25 00:00:00 kernel: [    1.015569] artosyn_typec_comphy_configure 894 conf 4 2700
Jan 25 00:00:00 kernel: [    1.015577] artosyn_dprx_init_phy 813 get lane info 4 4 0!
Jan 25 00:00:00 kernel: [    1.015583] artosyn_typec_reset_deassert 1546 5
Jan 25 00:00:00 kernel: [    1.015607] 0x2208: 000f0fff
Jan 25 00:00:00 kernel: [    1.015608] 0x2400: 00000000
Jan 25 00:00:00 kernel: [    1.015610] 0x2404: 00000000
Jan 25 00:00:00 kernel: [    1.015611] 0x2408: 00000000
Jan 25 00:00:00 kernel: [    1.015612] 0x240c: ffffffff
Jan 25 00:00:00 kernel: [    1.015613] 0x1104: 00000000
Jan 25 00:00:00 kernel: [    1.015614] 0x1108: 01000000
Jan 25 00:00:00 kernel: [    1.015615] 0x1118: 00000000
Jan 25 00:00:00 kernel: [    1.015617] 0x1134: 0401010a
Jan 25 00:00:00 kernel: [    1.015618] 0x1138: 00000000
Jan 25 00:00:00 kernel: [    1.015619] 0x113c: 00000000
Jan 25 00:00:00 kernel: [    1.015620] 0x1140: 8008ff00
Jan 25 00:00:00 kernel: [    1.016711] state change 3 -> 5
Jan 25 00:00:00 kernel: [    1.016729] pending state change 5 -> 6 @ 1000 ms
Jan 25 00:00:00 kernel: [    1.016738] training pattern change 1000,rate a,cnt 4, pattern 3
Jan 25 00:00:00 kernel: [    1.016740] 0x2208: 000fffff
Jan 25 00:00:00 kernel: [    1.016741] 0x2400: 00000055
Jan 25 00:00:00 kernel: [    1.016742] 0x2404: 00000055
Jan 25 00:00:00 kernel: [    1.016744] 0x2408: 00000000
Jan 25 00:00:00 kernel: [    1.016745] 0x240c: 67676767
Jan 25 00:00:00 kernel: [    1.016747] 0x1104: 0000000f
Jan 25 00:00:00 kernel: [    1.016748] 0x1108: 040f0000
Jan 25 00:00:00 kernel: [    1.016749] 0x1118: 00000000
Jan 25 00:00:00 kernel: [    1.016750] 0x1134: 0404010a
Jan 25 00:00:00 kernel: [    1.016751] 0x1138: 00000000
Jan 25 00:00:00 kernel: [    1.016752] 0x113c: 00000000
Jan 25 00:00:00 kernel: [    1.016753] 0x1140: 8009ff00
Jan 25 00:00:00 kernel: [    1.033414] iqs323 3-0044: iqs323_config is over : 0
Jan 25 00:00:00 kernel: [    1.033591] training pattern change 1010,rate a,cnt 4, pattern 0
Jan 25 00:00:00 kernel: [    1.033594] 0x2208: 000fffff
Jan 25 00:00:00 kernel: [    1.033596] 0x2400: 00000377
Jan 25 00:00:00 kernel: [    1.033597] 0x2404: 00000377
Jan 25 00:00:00 kernel: [    1.033598] 0x2408: 00000000
Jan 25 00:00:00 kernel: [    1.033599] 0x240c: fafafafa
Jan 25 00:00:00 kernel: [    1.033600] 0x1104: 000f000f
Jan 25 00:00:00 kernel: [    1.033601] 0x1108: 040f0000
Jan 25 00:00:00 kernel: [    1.033603] 0x1118: 00000000
Jan 25 00:00:00 kernel: [    1.033604] 0x1134: 0404010a
Jan 25 00:00:00 kernel: [    1.033605] 0x1138: 00000000
Jan 25 00:00:00 kernel: [    1.033606] 0x113c: 00000000
Jan 25 00:00:00 kernel: [    1.033607] 0x1140: 800cff00
Jan 25 00:00:00 kernel: [    1.033612] state change 5 -> 7
Jan 25 00:00:00 kernel: [    1.033620] training completed 1010(0xa 0xf:4)
Jan 25 00:00:00 kernel: [    1.033622] PHY 0x744: 088fc500
Jan 25 00:00:00 kernel: [    1.033625] 0x2208: 000fffff
Jan 25 00:00:00 kernel: [    1.033626] 0x2400: 00000377
Jan 25 00:00:00 kernel: [    1.033627] 0x2404: 00000377
Jan 25 00:00:00 kernel: [    1.033628] 0x2408: 00000000
Jan 25 00:00:00 kernel: [    1.033629] 0x240c: fafafafa
Jan 25 00:00:00 kernel: [    1.033630] 0x1104: 000f000f
Jan 25 00:00:00 kernel: [    1.033631] 0x1108: 040f0000
Jan 25 00:00:00 kernel: [    1.033633] 0x1118: 00000000
Jan 25 00:00:00 kernel: [    1.033634] 0x1134: 0404010a
Jan 25 00:00:00 kernel: [    1.033635] 0x1138: 00000000
Jan 25 00:00:00 kernel: [    1.033636] 0x113c: 00000000
Jan 25 00:00:00 kernel: [    1.033637] 0x1140: 800cff00
Jan 25 00:00:00 kernel: [    1.033638] PHY 0x3d8: 00000000
Jan 25 00:00:00 kernel: [    1.033639] PHY 0x43d8: 00000000
Jan 25 00:00:00 kernel: [    1.033641] PHY 0x83d8: 00000000
Jan 25 00:00:00 kernel: [    1.033642] PHY 0xc3d8: 00000000
Jan 25 00:00:00 kernel: [    1.033643] PHY 0x1414: 40400040
Jan 25 00:00:00 kernel: [    1.033644] PHY 0x44: 00000000
Jan 25 00:00:00 kernel: [    1.033646] PHY 0x20: 00006080
Jan 25 00:00:00 kernel: [    1.033647] PHY 0x73c: 0000003f
Jan 25 00:00:00 kernel: [    1.033648] PHY 0x14: 60692540
Jan 25 00:00:00 kernel: [    1.033651] Err info 0x0 0x0, 0x0 0x0, 0x0 0x0, 0x0 0x0
Jan 25 00:00:00 kernel: [    1.068554] using random self ethernet address
Jan 25 00:00:00 kernel: [    1.068563] using random host ethernet address
Jan 25 00:00:00 kernel: [    1.072442] using random self ethernet address
Jan 25 00:00:00 kernel: [    1.072451] using random host ethernet address
Jan 25 00:00:00 kernel: [    1.085660] file system registered
Jan 25 00:00:00 kernel: [    1.094591] Mass Storage Function, version: 2009/09/11
Jan 25 00:00:00 kernel: [    1.094605] LUN: removable file: (no medium)
Jan 25 00:00:00 kernel: [    1.102434] ec major : 236, minor : 0^M
Jan 25 00:00:00 kernel: [    1.102447] cdev_add su 
Jan 25 00:00:00 kernel: [    1.102514] class_create su 
Jan 25 00:00:00 kernel: [    1.102768] device_create su 
Jan 25 00:00:00 kernel: [    1.102851] smartPA_probe slave addr 53
Jan 25 00:00:00 kernel: [    1.102895] of_get_named_gpio = 44
Jan 25 00:00:00 kernel: [    1.102905] find label: HIGH
Jan 25 00:00:00 kernel: [    1.102910] gpio_direction_output: HIGH
Jan 25 00:00:00 kernel: [    1.102911] find label: smartPA_R
Jan 25 00:00:00 kernel: [    1.149036] artosyn_dprx_irq_thread_1 sdp start
Jan 25 00:00:00 kernel: [    1.179754] Read iProduct:XREAL One Pro
Jan 25 00:00:00 kernel: [    1.180633] usb0: HOST MAC fc:d2:b6:ad:cc:6d
Jan 25 00:00:00 kernel: [    1.180689] usb0: MAC fc:d2:b6:ad:cc:6a
Jan 25 00:00:00 kernel: [    1.181313] usb1: HOST MAC fc:d2:b6:ad:cc:6c
Jan 25 00:00:00 kernel: [    1.181346] usb1: MAC fc:d2:b6:ad:cc:6b
Jan 25 00:00:00 kernel: [    1.182198] Read iProduct:XREAL One Pro
Jan 25 00:00:00 kernel: [    1.182433] artosyn_kuiper_usb2phy_init 105
Jan 25 00:00:00 kernel: [    1.182467] dwc2 8080000.usb: bound driver configfs-gadget
Jan 25 00:00:00 kernel: [    1.198245] IPv6: ADDRCONF(NETDEV_UP): usb0: link is not ready
Jan 25 00:00:00 kernel: [    1.212765] IPv6: ADDRCONF(NETDEV_UP): usb1: link is not ready
Jan 25 00:00:00 kernel: [    1.302051] ec major : 236, minor : 1^M
Jan 25 00:00:00 kernel: [    1.302063] cdev_add su 
Jan 25 00:00:00 kernel: [    1.302123] class_create su 
Jan 25 00:00:00 kernel: [    1.302289] device_create su 
Jan 25 00:00:00 dhcpd: Internet Systems Consortium DHCP Server 4.4.3-P1
Jan 25 00:00:00 dhcpd: Copyright 2004-2022 Internet Systems Consortium.
Jan 25 00:00:00 dhcpd: All rights reserved.
Jan 25 00:00:00 dhcpd: For info, please visit https://www.isc.org/software/dhcp/
Jan 25 00:00:00 dhcpd: Wrote 0 leases to leases file.
Jan 25 00:00:00 kernel: [    1.332726] oled_open idx: 1
Jan 25 00:00:00 kernel: [    1.332834] oled_open idx: 0
Jan 25 00:00:00 kernel: [    1.339255] dwc2 8080000.usb: new device is high-speed
Jan 25 00:00:00 kernel: [    1.351938] dwc2 8080000.usb: new address 1
Jan 25 00:00:00 kernel: [    1.356950] OLED:ecx348_left initialized successfully with config_index: 2
Jan 25 00:00:00 kernel: [    1.356958] OLED initialized successfully with initial_code_idx: 2
Jan 25 00:00:00 kernel: [    1.358443] OLED:ecx348_right initialized successfully with config_index: 2
Jan 25 00:00:00 kernel: [    1.358445] OLED initialized successfully with initial_code_idx: 2
Jan 25 00:00:00 kernel: [    1.358608] Set orbit_h: 0
Jan 25 00:00:00 kernel: [    1.358709] Set orbit_h: 0
Jan 25 00:00:00 kernel: [    1.358806] Set orbit_v: 0
Jan 25 00:00:00 kernel: [    1.358903] Set orbit_v: 0
Jan 25 00:00:00 kernel: [    1.359056] Set white coordinate x:-13, y:-18
Jan 25 00:00:00 kernel: [    1.359098] Set white coordinate x:-13, y:-18
Jan 25 00:00:00 kernel: [    1.366812] configfs-gadget gadget: high-speed config #1: b
Jan 25 00:00:00 dhcpd: Listening on LPF/usb1/fc:d2:b6:ad:cc:6b/***********/24
Jan 25 00:00:00 dhcpd: Sending on   LPF/usb1/fc:d2:b6:ad:cc:6b/***********/24
Jan 25 00:00:00 kernel: [    1.390948] color mode 8000
Jan 25 00:00:00 kernel: [    1.390960] colorimetry 0
Jan 25 00:00:00 kernel: [    1.390963] 0 0 0 0
Jan 25 00:00:00 kernel: [    1.390966] video change
Jan 25 00:00:00 kernel: [    1.390968] format 0->0
Jan 25 00:00:00 kernel: [    1.390971] depth 0->1
Jan 25 00:00:00 kernel: [    1.390972] colorimetry 0->0
Jan 25 00:00:00 kernel: [    1.390975] hres 0->1920
Jan 25 00:00:00 kernel: [    1.390977] htotal 0->2200
Jan 25 00:00:00 kernel: [    1.390979] vres 0->1080
Jan 25 00:00:00 kernel: [    1.390980] vtotal 0->1125
Jan 25 00:00:00 kernel: [    1.390982] misc0_1 0x0->0x20
Jan 25 00:00:00 kernel: [    1.390984] mvid 0x0->0x699a
Jan 25 00:00:00 kernel: [    1.390985] nvid 0x0->0x8000
Jan 25 00:00:00 kernel: [    1.390987] vbid 0x0->0x11
Jan 25 00:00:00 kernel: [    1.390990] pixel clk 222753295
Jan 25 00:00:00 kernel: [    1.392280] Camera plug detect get_cam_plug_detect_config index = 0 
Jan 25 00:00:00 kernel: [    1.393728] ar_vb_open
Jan 25 00:00:00 kernel: [    1.401753] 6921d 2 3
Jan 25 00:00:00 kernel: [    1.412398] IPv6: ADDRCONF(NETDEV_CHANGE): usb1: link becomes ready
Jan 25 00:00:00 kernel: [    1.435418] color mode 8000
Jan 25 00:00:00 kernel: [    1.435427] colorimetry 0
Jan 25 00:00:00 kernel: [    1.435429] 0 0 0 0
Jan 25 00:00:00 kernel: [    1.435432] video change
Jan 25 00:00:00 kernel: [    1.435433] format 0->0
Jan 25 00:00:00 kernel: [    1.435435] depth 1->0
Jan 25 00:00:00 kernel: [    1.435436] colorimetry 0->0
Jan 25 00:00:00 kernel: [    1.435439] hres 1920->0
Jan 25 00:00:00 kernel: [    1.435442] htotal 2200->0
Jan 25 00:00:00 kernel: [    1.435444] vres 1080->0
Jan 25 00:00:00 kernel: [    1.435446] vtotal 1125->0
Jan 25 00:00:00 dhcpd: Listening on LPF/usb0/fc:d2:b6:ad:cc:6a/***********/24
Jan 25 00:00:00 kernel: [    1.435448] misc0_1 0x20->0x0
Jan 25 00:00:00 dhcpd: Sending on   LPF/usb0/fc:d2:b6:ad:cc:6a/***********/24
Jan 25 00:00:00 kernel: [    1.435449] mvid 0x699a->0x6900
Jan 25 00:00:00 kernel: [    1.435451] nvid 0x8000->0x8000
Jan 25 00:00:00 kernel: [    1.435452] vbid 0x11->0x19
Jan 25 00:00:00 kernel: [    1.435455] pixel clk 221484375
Jan 25 00:00:00 kernel: [    1.438781] ar_vb_open
Jan 25 00:00:00 dhcpd: Sending on   Socket/fallback/fallback-net
Jan 25 00:00:00 kernel: [    1.438933] hil_mmb_alloc pa:0x3cc04000 len:32768!
Jan 25 00:00:00 dhcpd: Server starting service.
Jan 25 00:00:01 kernel: [    1.457354] hil_mmb_alloc pa:0x3cc0d000 len:65536!
Jan 25 00:00:01 kernel: [    1.460090] ar_vb_open
Jan 25 00:00:01 kernel: [    1.463668] start success
Jan 25 00:00:01 kernel: [    1.469463] start success
Jan 25 00:00:01 kernel: [    1.531029] binder: 497:624 refcount change on invalid ref 0
Jan 25 00:00:01 dhcpd: DHCPDISCOVER from fc:d2:b6:ad:cc:6c via usb1
Jan 25 00:00:01 dhcpd: DHCPOFFER on 169.254.1.10 to fc:d2:b6:ad:cc:6c (SijiesMBP133m1) via usb1
Jan 25 00:00:01 kernel: [    1.664417] color mode 8000
Jan 25 00:00:01 kernel: [    1.664429] colorimetry 0
Jan 25 00:00:01 kernel: [    1.664431] 0 0 0 0
Jan 25 00:00:01 kernel: [    1.664433] video change
Jan 25 00:00:01 kernel: [    1.664435] format 0->0
Jan 25 00:00:01 kernel: [    1.664437] depth 0->1
Jan 25 00:00:01 kernel: [    1.664439] colorimetry 0->0
Jan 25 00:00:01 kernel: [    1.664441] hres 0->1920
Jan 25 00:00:01 kernel: [    1.664443] htotal 0->2200
Jan 25 00:00:01 kernel: [    1.664444] vres 0->1080
Jan 25 00:00:01 kernel: [    1.664446] vtotal 0->1125
Jan 25 00:00:01 kernel: [    1.664448] misc0_1 0x0->0x20
Jan 25 00:00:01 kernel: [    1.664450] mvid 0x6900->0x8cce
Jan 25 00:00:01 kernel: [    1.664451] nvid 0x8000->0x8000
Jan 25 00:00:01 kernel: [    1.664453] vbid 0x19->0x11
Jan 25 00:00:01 kernel: [    1.664455] pixel clk 297009887
Jan 25 00:00:01 kernel: [    1.667255] IPv6: ADDRCONF(NETDEV_CHANGE): usb0: link becomes ready
Jan 25 00:00:01 kernel: [    1.672265] 6921d 2 3
Jan 25 00:00:01 kernel: [    1.942403] Open is called.
Jan 25 00:00:01 kernel: [    1.943305] Open is called.
Jan 25 00:00:01 kernel: [    1.947544] Open is called.
Jan 25 00:00:01 kernel: [    1.947582] dev_0 request  IRQ success
Jan 25 00:00:01 kernel: [    1.951449] dev_1 request  IRQ success
Jan 25 00:00:01 kernel: [    1.952874] Enabling LVDS lowest power...
Jan 25 00:00:01 kernel: [    1.952885] Write Value: 0x80 to Address: 0xffffff8008edd018
Jan 25 00:00:01 kernel: [    1.952887] Verification successful at Address: 0xffffff8008edd018. Value: 0x80
Jan 25 00:00:01 kernel: [    1.952889] Write Value: 0x1a to Address: 0xffffff8008edd390
Jan 25 00:00:01 kernel: [    1.952891] Verification successful at Address: 0xffffff8008edd390. Value: 0x1a
Jan 25 00:00:01 kernel: [    1.952893] Write Value: 0x1 to Address: 0xffffff8008edd0c0
Jan 25 00:00:01 kernel: [    1.952895] Verification successful at Address: 0xffffff8008edd0c0. Value: 0x1
Jan 25 00:00:01 kernel: [    1.952897] Write Value: 0xc0 to Address: 0xffffff8008edd39c
Jan 25 00:00:01 kernel: [    1.952899] Verification successful at Address: 0xffffff8008edd39c. Value: 0xc0
Jan 25 00:00:01 kernel: [    1.952901] Write Value: 0xad to Address: 0xffffff8008edd3a0
Jan 25 00:00:01 kernel: [    1.952903] Verification successful at Address: 0xffffff8008edd3a0. Value: 0xad
Jan 25 00:00:01 kernel: [    1.952907] Write Value: 0x80 to Address: 0xffffff8008f03418
Jan 25 00:00:01 kernel: [    1.952909] Verification successful at Address: 0xffffff8008f03418. Value: 0x80
Jan 25 00:00:01 kernel: [    1.952911] Write Value: 0x1a to Address: 0xffffff8008f03790
Jan 25 00:00:01 kernel: [    1.952913] Verification successful at Address: 0xffffff8008f03790. Value: 0x1a
Jan 25 00:00:01 kernel: [    1.952914] Write Value: 0x1 to Address: 0xffffff8008f034c0
Jan 25 00:00:01 kernel: [    1.952916] Verification successful at Address: 0xffffff8008f034c0. Value: 0x1
Jan 25 00:00:01 kernel: [    1.952918] Write Value: 0xc0 to Address: 0xffffff8008f0379c
Jan 25 00:00:01 kernel: [    1.952920] Verification successful at Address: 0xffffff8008f0379c. Value: 0xc0
Jan 25 00:00:01 kernel: [    1.952922] Write Value: 0xad to Address: 0xffffff8008f037a0
Jan 25 00:00:01 kernel: [    1.952924] Verification successful at Address: 0xffffff8008f037a0. Value: 0xad
Jan 25 00:00:01 kernel: [    1.952926] Write Value: 0x80 to Address: 0xffffff8008f05018
Jan 25 00:00:01 kernel: [    1.952928] Verification successful at Address: 0xffffff8008f05018. Value: 0x80
Jan 25 00:00:01 kernel: [    1.952930] Write Value: 0x1a to Address: 0xffffff8008f05390
Jan 25 00:00:01 kernel: [    1.952932] Verification successful at Address: 0xffffff8008f05390. Value: 0x1a
Jan 25 00:00:01 kernel: [    1.952934] Write Value: 0x1 to Address: 0xffffff8008f050c0
Jan 25 00:00:01 kernel: [    1.952936] Verification successful at Address: 0xffffff8008f050c0. Value: 0x1
Jan 25 00:00:01 kernel: [    1.952937] Write Value: 0xc0 to Address: 0xffffff8008f0539c
Jan 25 00:00:01 kernel: [    1.952939] Verification successful at Address: 0xffffff8008f0539c. Value: 0xc0
Jan 25 00:00:01 kernel: [    1.952941] Write Value: 0xad to Address: 0xffffff8008f053a0
Jan 25 00:00:01 kernel: [    1.952943] Verification successful at Address: 0xffffff8008f053a0. Value: 0xad
Jan 25 00:00:01 kernel: [    1.952945] Write Value: 0x80 to Address: 0xffffff8008f07418
Jan 25 00:00:01 kernel: [    1.952947] Verification successful at Address: 0xffffff8008f07418. Value: 0x80
Jan 25 00:00:01 kernel: [    1.952949] Write Value: 0x1a to Address: 0xffffff8008f07790
Jan 25 00:00:01 kernel: [    1.952951] Verification successful at Address: 0xffffff8008f07790. Value: 0x1a
Jan 25 00:00:01 kernel: [    1.952952] Write Value: 0x1 to Address: 0xffffff8008f074c0
Jan 25 00:00:01 kernel: [    1.952954] Verification successful at Address: 0xffffff8008f074c0. Value: 0x1
Jan 25 00:00:01 kernel: [    1.952956] Write Value: 0xc0 to Address: 0xffffff8008f0779c
Jan 25 00:00:01 kernel: [    1.952958] Verification successful at Address: 0xffffff8008f0779c. Value: 0xc0
Jan 25 00:00:01 kernel: [    1.952960] Write Value: 0xad to Address: 0xffffff8008f077a0
Jan 25 00:00:01 kernel: [    1.952962] Verification successful at Address: 0xffffff8008f077a0. Value: 0xad
Jan 25 00:00:01 kernel: [    1.953262] ar_vb_open
Jan 25 00:00:01 dhcpd: DHCPDISCOVER from fc:d2:b6:ad:cc:6d via usb0
Jan 25 00:00:01 dhcpd: DHCPOFFER on ************ to fc:d2:b6:ad:cc:6d (SijiesMBP133m1) via usb0
Jan 25 00:00:01 kernel: [    2.207122] random: crng init done
Jan 25 00:00:02 kernel: [    2.541685] inv_mpu: imu fifo count mismatch, fifo=1, irq_ts=0
Jan 25 00:00:02 kernel: [    2.581710] inv_mpu: imu fifo count mismatch, fifo=1, irq_ts=0
Jan 25 00:00:02 kernel: [    2.682222] 69000 1 8
Jan 25 00:00:02 kernel: [    2.683494] 69008 1 3
Jan 25 00:00:02 kernel: [    2.785170] 6900b 2 10
Jan 25 00:00:02 kernel: [    2.785771] 6901b 2 10
Jan 25 00:00:02 kernel: [    2.786348] 6902b 2 10
Jan 25 00:00:02 kernel: [    2.786944] 6903b 2 10
Jan 25 00:00:02 kernel: [    2.787564] 6904b 2 10
Jan 25 00:00:02 kernel: [    2.788156] 6905b 2 10
Jan 25 00:00:02 kernel: [    2.788735] 6906b 2 10
Jan 25 00:00:02 kernel: [    2.789328] 6907b 2 10
Jan 25 00:00:02 kernel: [    2.789996] 6908b 2 10
Jan 25 00:00:02 kernel: [    2.790594] 6909b 2 10
Jan 25 00:00:02 kernel: [    2.791285] 690ab 2 10
Jan 25 00:00:02 kernel: [    2.791993] 690bb 2 10
Jan 25 00:00:02 kernel: [    2.792575] 690cb 2 10
Jan 25 00:00:02 kernel: [    2.793155] 690db 2 10
Jan 25 00:00:02 kernel: [    2.793735] 690eb 2 10
Jan 25 00:00:02 kernel: [    2.794317] 690fb 2 10
Jan 25 00:00:02 kernel: [    2.794903] 6910b 2 10
Jan 25 00:00:02 kernel: [    2.795491] 6911b 2 10
Jan 25 00:00:02 kernel: [    2.796078] 6912b 2 10
Jan 25 00:00:02 kernel: [    2.796676] 6913b 2 10
Jan 25 00:00:02 kernel: [    2.797255] 6914b 2 10
Jan 25 00:00:02 kernel: [    2.797841] 6915b 2 10
Jan 25 00:00:02 kernel: [    2.798443] 6916b 2 10
Jan 25 00:00:02 kernel: [    2.799035] 6917b 2 10
Jan 25 00:00:02 kernel: [    2.800223] 6918b 2 10
Jan 25 00:00:02 kernel: [    2.800809] 6919b 2 10
Jan 25 00:00:02 kernel: [    2.801384] 691ab 2 10
Jan 25 00:00:02 kernel: [    2.801955] 691bb 2 10
Jan 25 00:00:02 kernel: [    2.802526] 691cb 2 10
Jan 25 00:00:02 kernel: [    2.803105] 691db 2 10
Jan 25 00:00:02 kernel: [    2.803699] 691eb 2 10
Jan 25 00:00:02 kernel: [    2.804302] 691fb 2 10
Jan 25 00:00:02 kernel: [    2.804878] 6920b 2 a
Jan 25 00:00:02 kernel: [    2.805610] 69215 2 8
Jan 25 00:00:02 kernel: [    2.806616] 6921d 2 3
Jan 25 00:00:02 kernel: [    2.808181] 692a0 1 10
Jan 25 00:00:02 kernel: [    2.808978] 692b0 1 10
Jan 25 00:00:02 kernel: [    2.808996] artosyn_dprx_irq_thread_1 692b0
Jan 25 00:00:02 kernel: [    2.809003] artosyn_dprx_hdcp_2_2_calculation 692b0
Jan 25 00:00:02 kernel: [    2.811167] store km: 33 3b 09 5f
Jan 25 00:00:02 kernel: [    2.811175] store km: 7a 77 a3 86
Jan 25 00:00:02 kernel: [    2.811177] store km: 62 ca 2c f5
Jan 25 00:00:02 kernel: [    2.811178] store km: f2 df 29 da
Jan 25 00:00:02 kernel: [    2.811188] cal done 10000
Jan 25 00:00:02 kernel: [    2.811193] cal done 10000
Jan 25 00:00:02 kernel: [    2.811196] hw kd: 72 4d 23 c3
Jan 25 00:00:02 kernel: [    2.811198] hw kd: 47 f8 67 71
Jan 25 00:00:02 kernel: [    2.811200] hw kd: b0 f2 6c 69
Jan 25 00:00:02 kernel: [    2.811201] hw kd: 94 41 ec ba
Jan 25 00:00:02 kernel: [    2.811203] hw kd: 66 68 7a b2
Jan 25 00:00:02 kernel: [    2.811204] hw kd: 17 b2 2a c1
Jan 25 00:00:02 kernel: [    2.811206] hw kd: b0 c1 02 27
Jan 25 00:00:02 kernel: [    2.811208] hw kd: c5 a5 a8 db
Jan 25 00:00:02 kernel: [    2.812892] h: bc 05 9c 08
Jan 25 00:00:02 kernel: [    2.812899] h: bc 97 d3 94
Jan 25 00:00:02 kernel: [    2.812900] h: 81 19 76 f7
Jan 25 00:00:02 kernel: [    2.812902] h: de 9c 31 dc
Jan 25 00:00:02 kernel: [    2.812903] h: 53 4b 04 ed
Jan 25 00:00:02 kernel: [    2.812905] h: 19 33 b0 0d
Jan 25 00:00:02 kernel: [    2.812906] h: 7c 3c 0a 02
Jan 25 00:00:02 kernel: [    2.812908] h: c3 87 24 2b
Jan 25 00:00:02 kernel: [    2.812918] artosyn_typec_event 1631 1!
Jan 25 00:00:02 kernel: [    2.812929] dp_sink_altmode_event 485 1 dp->hpd:1 hpd:1 irq:256
Jan 25 00:00:02 kernel: [    2.812936] dp_sink_altmode_attention_vdm 300
Jan 25 00:00:02 kernel: [    2.812997] artosyn_dprx_dp_pd_event_work 2879 1
Jan 25 00:00:02 kernel: [    2.836073] 69493 2 1
Jan 25 00:00:02 kernel: [    2.836081] rx_status 2
Jan 25 00:00:02 kernel: [    2.836918] 692c0 2 10
Jan 25 00:00:02 kernel: [    2.837509] 692d0 2 10
Jan 25 00:00:02 kernel: [    2.839136] 692f0 1 8
Jan 25 00:00:02 kernel: [    2.839143] L' start
Jan 25 00:00:02 kernel: [    2.839160] artosyn_dprx_irq_thread_1 692f0
Jan 25 00:00:02 kernel: [    2.839162] artosyn_dprx_hdcp_2_2_calculation 692f0
Jan 25 00:00:02 kernel: [    2.839223] L' end
Jan 25 00:00:02 kernel: [    2.856759] 692f8 2 10
Jan 25 00:00:02 kernel: [    2.857537] 69308 2 10
Jan 25 00:00:02 kernel: [    2.859766] 69318 1 10
Jan 25 00:00:02 kernel: [    2.860437] 69328 1 8
Jan 25 00:00:02 kernel: [    2.860451] artosyn_dprx_irq_thread_1 69328
Jan 25 00:00:02 kernel: [    2.860454] artosyn_dprx_hdcp_2_2_calculation 69328
Jan 25 00:00:02 kernel: [    2.860462] cal done 10000
Jan 25 00:00:02 kernel: [    2.860465] hw kd2: e7 e0 0d f6
Jan 25 00:00:02 kernel: [    2.860467] hw kd2: 36 20 47 00
Jan 25 00:00:02 kernel: [    2.860469] hw kd2: 9e 8c 18 d0
Jan 25 00:00:02 kernel: [    2.860470] hw kd2: 68 f7 a9 b5
Jan 25 00:00:02 kernel: [    2.860472] r_n: 72 34 2e 70
Jan 25 00:00:02 kernel: [    2.860474] r_n: a1 a8 e8 49
Jan 25 00:00:02 kernel: [    2.860476] tx_r: ce 22 d8 05
Jan 25 00:00:02 kernel: [    2.860478] tx_r: 5b 8d ff dc
Jan 25 00:00:02 kernel: [    2.860480] rx_r: e2 d6 83 02
Jan 25 00:00:02 kernel: [    2.860481] rx_r: 0f 0e e1 71
Jan 25 00:00:02 kernel: [    2.860483] e_dkey_ks: 40 a6 bb b1
Jan 25 00:00:02 kernel: [    2.860485] e_dkey_ks: 89 df 22 d0
Jan 25 00:00:02 kernel: [    2.860486] e_dkey_ks: 27 59 ad f0
Jan 25 00:00:02 kernel: [    2.860488] e_dkey_ks: 28 d4 73 64
Jan 25 00:00:02 kernel: [    2.860490] ks: a7 46 b6 47
Jan 25 00:00:02 kernel: [    2.860491] ks: bf ff 65 d0
Jan 25 00:00:02 kernel: [    2.860494] ks: 5b 03 36 22
Jan 25 00:00:02 kernel: [    2.860496] ks: 4f 2d 3b a0
Jan 25 00:00:02 kernel: [    2.860502] lc: b5 d8 e9 ab
Jan 25 00:00:02 kernel: [    2.860504] lc: 5f 8a fe ca
Jan 25 00:00:02 kernel: [    2.860505] lc: 38 55 b1 a5
Jan 25 00:00:02 kernel: [    2.860507] lc: 1e c9 bc 0f
Jan 25 00:00:02 kernel: [    2.860508] r_iv: 11 48 a7 ad
Jan 25 00:00:02 kernel: [    2.860510] r_iv: 81 23 53 fb
Jan 25 00:00:02 kernel: [    2.860511] artosyn_dprx_hdcp_2_2_start
Jan 25 00:00:02 kernel: [    2.860971] 69494 1 1
Jan 25 00:00:02 kernel: [    2.860974] type 1
Jan 25 00:00:02 kernel: [    2.860976] tmp_r_iv: 11 48 a7 ad
Jan 25 00:00:02 kernel: [    2.860978] tmp_r_iv: 81 23 53 fa
Jan 25 00:00:02 dhcpd: DHCPREQUEST for 169.254.1.10 (169.254.1.1) from fc:d2:b6:ad:cc:6c (SijiesMBP133m1) via usb1
Jan 25 00:00:02 dhcpd: DHCPACK on 169.254.1.10 to fc:d2:b6:ad:cc:6c (SijiesMBP133m1) via usb1
Jan 25 00:00:02 dhcpd: DHCPREQUEST for ************ (169.254.2.1) from fc:d2:b6:ad:cc:6d (SijiesMBP133m1) via usb0
Jan 25 00:00:02 dhcpd: DHCPACK on ************ to fc:d2:b6:ad:cc:6d (SijiesMBP133m1) via usb0
Jan 25 00:00:03 kernel: [    3.640339] get_ts_config index = 0 
Jan 25 00:00:03 kernel: [    3.640358] timestamp_record_open, clear kfifo and enable irq, sensor index = 0 
Jan 25 00:00:03 kernel: [    3.690220] dev_0 SUB_CLEAR success
Jan 25 00:00:03 kernel: [    3.690886] dev_0 free IRQ success
Jan 25 00:00:03 kernel: [    3.691816] dev_1 SUB_CLEAR success
Jan 25 00:00:03 kernel: [    3.692113] dev_1 free IRQ success
Jan 25 00:00:03 kernel: [    3.698226] mmz_userdev:get_mmbinfo_safe: 
Jan 25 00:00:03 kernel: [    3.698233] mmb(0x3CC66000) not found!
Jan 25 00:00:03 kernel: [    3.698311] mmz_userdev:get_mmbinfo_safe: 
Jan 25 00:00:03 kernel: [    3.698313] mmb(0x3CC67000) not found!
Jan 25 00:00:03 kernel: [    3.698354] mmz_userdev:get_mmbinfo_safe: 
Jan 25 00:00:03 kernel: [    3.698356] mmb(0x3CC68000) not found!
Jan 25 00:00:03 kernel: [    3.698398] mmz_userdev:get_mmbinfo_safe: 
Jan 25 00:00:03 kernel: [    3.698400] mmb(0x3CC69000) not found!
Jan 25 00:00:03 kernel: [    3.698443] mmz_userdev:get_mmbinfo_safe: 
Jan 25 00:00:03 kernel: [    3.698445] mmb(0x3CC6A000) not found!
Jan 25 00:00:03 kernel: [    3.698499] mmz_userdev:get_mmbinfo_safe: 
Jan 25 00:00:03 kernel: [    3.698501] mmb(0x3CC6B000) not found!
Jan 25 00:00:03 kernel: [    3.698543] mmz_userdev:get_mmbinfo_safe: 
Jan 25 00:00:03 kernel: [    3.698544] mmb(0x3CC6C000) not found!
Jan 25 00:00:03 kernel: [    3.698582] mmz_userdev:get_mmbinfo_safe: 
Jan 25 00:00:03 kernel: [    3.698584] mmb(0x3CC6D000) not found!
Jan 25 00:00:03 kernel: [    3.715844] OLED:ecx348_left initialized successfully with config_index: 3
Jan 25 00:00:03 kernel: [    3.715855] OLED initialized successfully with initial_code_idx: 3
Jan 25 00:00:03 kernel: [    3.717194] OLED:ecx348_right initialized successfully with config_index: 3
Jan 25 00:00:03 kernel: [    3.717196] OLED initialized successfully with initial_code_idx: 3
Jan 25 00:00:03 kernel: [    3.717334] Set orbit_h: 0
Jan 25 00:00:03 kernel: [    3.717420] Set orbit_h: 0
Jan 25 00:00:03 kernel: [    3.717505] Set orbit_v: 0
Jan 25 00:00:03 kernel: [    3.717644] Set orbit_v: 0
Jan 25 00:00:03 kernel: [    3.921744] dev_0 request  IRQ success
Jan 25 00:00:03 kernel: [    3.924180] dev_1 request  IRQ success
Jan 25 00:00:03 kernel: [    3.925484] Enabling LVDS lowest power...
Jan 25 00:00:03 kernel: [    3.925493] Write Value: 0x80 to Address: 0xffffff8008edd018
Jan 25 00:00:03 kernel: [    3.925496] Verification successful at Address: 0xffffff8008edd018. Value: 0x80
Jan 25 00:00:03 kernel: [    3.925497] Write Value: 0x1a to Address: 0xffffff8008edd390
Jan 25 00:00:03 kernel: [    3.925499] Verification successful at Address: 0xffffff8008edd390. Value: 0x1a
Jan 25 00:00:03 kernel: [    3.925501] Write Value: 0x1 to Address: 0xffffff8008edd0c0
Jan 25 00:00:03 kernel: [    3.925502] Verification successful at Address: 0xffffff8008edd0c0. Value: 0x1
Jan 25 00:00:03 kernel: [    3.925504] Write Value: 0xc0 to Address: 0xffffff8008edd39c
Jan 25 00:00:03 kernel: [    3.925506] Verification successful at Address: 0xffffff8008edd39c. Value: 0xc0
Jan 25 00:00:03 kernel: [    3.925507] Write Value: 0xad to Address: 0xffffff8008edd3a0
Jan 25 00:00:03 kernel: [    3.925509] Verification successful at Address: 0xffffff8008edd3a0. Value: 0xad
Jan 25 00:00:03 kernel: [    3.925511] Write Value: 0x80 to Address: 0xffffff8008f03418
Jan 25 00:00:03 kernel: [    3.925513] Verification successful at Address: 0xffffff8008f03418. Value: 0x80
Jan 25 00:00:03 kernel: [    3.925514] Write Value: 0x1a to Address: 0xffffff8008f03790
Jan 25 00:00:03 kernel: [    3.925516] Verification successful at Address: 0xffffff8008f03790. Value: 0x1a
Jan 25 00:00:03 kernel: [    3.925517] Write Value: 0x1 to Address: 0xffffff8008f034c0
Jan 25 00:00:03 kernel: [    3.925519] Verification successful at Address: 0xffffff8008f034c0. Value: 0x1
Jan 25 00:00:03 kernel: [    3.925520] Write Value: 0xc0 to Address: 0xffffff8008f0379c
Jan 25 00:00:03 kernel: [    3.925522] Verification successful at Address: 0xffffff8008f0379c. Value: 0xc0
Jan 25 00:00:03 kernel: [    3.925523] Write Value: 0xad to Address: 0xffffff8008f037a0
Jan 25 00:00:03 kernel: [    3.925525] Verification successful at Address: 0xffffff8008f037a0. Value: 0xad
Jan 25 00:00:03 kernel: [    3.925528] Write Value: 0x80 to Address: 0xffffff8008f05018
Jan 25 00:00:03 kernel: [    3.925530] Verification successful at Address: 0xffffff8008f05018. Value: 0x80
Jan 25 00:00:03 kernel: [    3.925531] Write Value: 0x1a to Address: 0xffffff8008f05390
Jan 25 00:00:03 kernel: [    3.925533] Verification successful at Address: 0xffffff8008f05390. Value: 0x1a
Jan 25 00:00:03 kernel: [    3.925535] Write Value: 0x1 to Address: 0xffffff8008f050c0
Jan 25 00:00:03 kernel: [    3.925536] Verification successful at Address: 0xffffff8008f050c0. Value: 0x1
Jan 25 00:00:03 kernel: [    3.925538] Write Value: 0xc0 to Address: 0xffffff8008f0539c
Jan 25 00:00:03 kernel: [    3.925540] Verification successful at Address: 0xffffff8008f0539c. Value: 0xc0
Jan 25 00:00:03 kernel: [    3.925541] Write Value: 0xad to Address: 0xffffff8008f053a0
Jan 25 00:00:03 kernel: [    3.925543] Verification successful at Address: 0xffffff8008f053a0. Value: 0xad
Jan 25 00:00:03 kernel: [    3.925544] Write Value: 0x80 to Address: 0xffffff8008f07418
Jan 25 00:00:03 kernel: [    3.925546] Verification successful at Address: 0xffffff8008f07418. Value: 0x80
Jan 25 00:00:03 kernel: [    3.925548] Write Value: 0x1a to Address: 0xffffff8008f07790
Jan 25 00:00:03 kernel: [    3.925550] Verification successful at Address: 0xffffff8008f07790. Value: 0x1a
Jan 25 00:00:03 kernel: [    3.925551] Write Value: 0x1 to Address: 0xffffff8008f074c0
Jan 25 00:00:03 kernel: [    3.925553] Verification successful at Address: 0xffffff8008f074c0. Value: 0x1
Jan 25 00:00:03 kernel: [    3.925554] Write Value: 0xc0 to Address: 0xffffff8008f0779c
Jan 25 00:00:03 kernel: [    3.925556] Verification successful at Address: 0xffffff8008f0779c. Value: 0xc0
Jan 25 00:00:03 kernel: [    3.925559] Write Value: 0xad to Address: 0xffffff8008f077a0
Jan 25 00:00:03 kernel: [    3.925561] Verification successful at Address: 0xffffff8008f077a0. Value: 0xad
Jan 25 00:00:03 kernel: [    3.927778] Get switch state: 0
Jan 25 00:00:03 kernel: [    3.927866] Get switch state: 0
Jan 25 00:00:03 kernel: [    3.949268] Brightness register values: 00 00
Jan 25 00:00:03 kernel: [    3.949277] Get brightness: 0
Jan 25 00:00:03 kernel: [    3.949361] Brightness register values: 00 00
Jan 25 00:00:03 kernel: [    3.949363] Get brightness: 0
Jan 25 00:00:03 kernel: [    3.950416] sony oled spi32766.0: Device is ecx348, skipping ECX343E/F distinction.
Jan 25 00:00:03 kernel: [    3.950424] sony oled spi32766.0: OLED type detected: ECX348
Jan 25 00:00:03 kernel: [    3.950430] Get switch state: 1
Jan 25 00:00:03 kernel: [    3.950498] Get switch state: 1
Jan 25 00:00:03 kernel: [    3.953731] Set white coordinate x:-31, y:-31
Jan 25 00:00:03 kernel: [    3.954985] Set white coordinate x:-31, y:-31
Jan 25 00:00:03 kernel: [    3.978412] Brightness register values: 00 00
Jan 25 00:00:03 kernel: [    3.978422] Get brightness: 0
Jan 25 00:00:03 kernel: [    3.978529] Brightness register values: 00 00
Jan 25 00:00:03 kernel: [    3.978531] Get brightness: 0
Jan 25 00:00:03 kernel: [    3.987444] exFAT-fs (mmcblk0p22[259:14]): trying to mount...
Jan 25 00:00:03 kernel: [    3.988092] exFAT-fs (mmcblk0p22[259:14]): set logical sector size  : 512
Jan 25 00:00:03 kernel: [    3.988100] exFAT-fs (mmcblk0p22[259:14]): (bps : 512, spc : 64, data start : 3072, aligned)
Jan 25 00:00:03 kernel: [    3.988105] exFAT-fs (mmcblk0p22[259:14]): detected volume size     : 2097152 KB (disk : 3829760 KB, part : 2097152 KB)
Jan 25 00:00:03 kernel: [    3.993802] exFAT-fs (mmcblk0p22[259:14]): mounted successfully!
Jan 25 00:00:03 kernel: [    4.018404] Brightness register values: 00 32
Jan 25 00:00:03 kernel: [    4.018415] Get brightness: 50
Jan 25 00:00:03 kernel: [    4.018500] Brightness register values: 00 32
Jan 25 00:00:03 kernel: [    4.018502] Get brightness: 50
Jan 25 00:00:03 kernel: [    4.058365] Brightness register values: 00 64
Jan 25 00:00:03 kernel: [    4.058376] Get brightness: 100
Jan 25 00:00:03 kernel: [    4.058462] Brightness register values: 00 64
Jan 25 00:00:03 kernel: [    4.058463] Get brightness: 100
Jan 25 00:00:03 kernel: [    4.098453] Brightness register values: 00 96
Jan 25 00:00:03 kernel: [    4.098470] Get brightness: 150
Jan 25 00:00:03 kernel: [    4.098591] Brightness register values: 00 96
Jan 25 00:00:03 kernel: [    4.098594] Get brightness: 150
Jan 25 00:00:03 kernel: [    4.138345] Brightness register values: 00 c8
Jan 25 00:00:03 kernel: [    4.138354] Get brightness: 200
Jan 25 00:00:03 kernel: [    4.138440] Brightness register values: 00 c8
Jan 25 00:00:03 kernel: [    4.138442] Get brightness: 200
Jan 25 00:00:03 kernel: [    4.178353] Brightness register values: 00 fa
Jan 25 00:00:03 kernel: [    4.178362] Get brightness: 250
Jan 25 00:00:03 kernel: [    4.178455] Brightness register values: 00 fa
Jan 25 00:00:03 kernel: [    4.178457] Get brightness: 250
Jan 25 00:00:03 kernel: [    4.218342] Brightness register values: 01 2c
Jan 25 00:00:03 kernel: [    4.218352] Get brightness: 300
Jan 25 00:00:03 kernel: [    4.218439] Brightness register values: 01 2c
Jan 25 00:00:03 kernel: [    4.218441] Get brightness: 300
Jan 25 00:00:03 kernel: [    4.258391] Brightness register values: 01 5e
Jan 25 00:00:03 kernel: [    4.258400] Get brightness: 350
Jan 25 00:00:03 kernel: [    4.258496] Brightness register values: 01 5e
Jan 25 00:00:03 kernel: [    4.258498] Get brightness: 350
Jan 25 00:00:03 kernel: [    4.298718] Brightness register values: 01 90
Jan 25 00:00:03 kernel: [    4.298728] Get brightness: 400
Jan 25 00:00:03 kernel: [    4.298840] Brightness register values: 01 90
Jan 25 00:00:03 kernel: [    4.298842] Get brightness: 400
Jan 25 00:00:03 kernel: [    4.338375] Brightness register values: 01 c2
Jan 25 00:00:03 kernel: [    4.338390] Get brightness: 450
Jan 25 00:00:03 kernel: [    4.338487] Brightness register values: 01 c2
Jan 25 00:00:03 kernel: [    4.338488] Get brightness: 450
Jan 25 00:03:05 kernel: [  186.178470] Get switch state: 1
Jan 25 00:03:05 kernel: [  186.178562] Get switch state: 1
Jan 25 00:03:05 kernel: [  186.182794] Get switch state: 1
Jan 25 00:03:05 kernel: [  186.182887] Get switch state: 1
Jan 25 00:03:05 kernel: [  186.183786] Brightness register values: 01 f4
Jan 25 00:03:05 kernel: [  186.183799] Get brightness: 500
Jan 25 00:03:05 kernel: [  186.183876] Brightness register values: 01 f4
Jan 25 00:03:05 kernel: [  186.183877] Get brightness: 500
Jan 25 00:03:05 kernel: [  186.293199] dev_0 SUB_CLEAR success
Jan 25 00:03:05 kernel: [  186.293393] dev_0 free IRQ success
Jan 25 00:03:05 kernel: [  186.294276] dev_1 SUB_CLEAR success
Jan 25 00:03:05 kernel: [  186.294519] dev_1 free IRQ success
Jan 25 00:03:05 kernel: [  186.317160] OLED:ecx348_left initialized successfully with config_index: 3
Jan 25 00:03:05 kernel: [  186.317168] OLED initialized successfully with initial_code_idx: 3
Jan 25 00:03:05 kernel: [  186.318522] OLED:ecx348_right initialized successfully with config_index: 3
Jan 25 00:03:05 kernel: [  186.318525] OLED initialized successfully with initial_code_idx: 3
Jan 25 00:03:05 kernel: [  186.318694] Set orbit_h: 0
Jan 25 00:03:05 kernel: [  186.318773] Set orbit_h: 0
Jan 25 00:03:05 kernel: [  186.318851] Set orbit_v: 0
Jan 25 00:03:05 kernel: [  186.318928] Set orbit_v: 0
Jan 25 00:03:05 kernel: [  186.425276] timestamp_record_release, disable irq, sensor index = 0 
Jan 25 00:03:06 kernel: [  186.522187] dev_0 request  IRQ success
Jan 25 00:03:06 kernel: [  186.524783] dev_1 request  IRQ success
Jan 25 00:03:06 kernel: [  186.525940] Enabling LVDS lowest power...
Jan 25 00:03:06 kernel: [  186.525950] Write Value: 0x80 to Address: 0xffffff8008edd018
Jan 25 00:03:06 kernel: [  186.525953] Verification successful at Address: 0xffffff8008edd018. Value: 0x80
Jan 25 00:03:06 kernel: [  186.525954] Write Value: 0x1a to Address: 0xffffff8008edd390
Jan 25 00:03:06 kernel: [  186.525956] Verification successful at Address: 0xffffff8008edd390. Value: 0x1a
Jan 25 00:03:06 kernel: [  186.525958] Write Value: 0x1 to Address: 0xffffff8008edd0c0
Jan 25 00:03:06 kernel: [  186.525959] Verification successful at Address: 0xffffff8008edd0c0. Value: 0x1
Jan 25 00:03:06 kernel: [  186.525961] Write Value: 0xc0 to Address: 0xffffff8008edd39c
Jan 25 00:03:06 kernel: [  186.525963] Verification successful at Address: 0xffffff8008edd39c. Value: 0xc0
Jan 25 00:03:06 kernel: [  186.525964] Write Value: 0xad to Address: 0xffffff8008edd3a0
Jan 25 00:03:06 kernel: [  186.525966] Verification successful at Address: 0xffffff8008edd3a0. Value: 0xad
Jan 25 00:03:06 kernel: [  186.525968] Write Value: 0x80 to Address: 0xffffff8008f03418
Jan 25 00:03:06 kernel: [  186.525970] Verification successful at Address: 0xffffff8008f03418. Value: 0x80
Jan 25 00:03:06 kernel: [  186.525971] Write Value: 0x1a to Address: 0xffffff8008f03790
Jan 25 00:03:06 kernel: [  186.525973] Verification successful at Address: 0xffffff8008f03790. Value: 0x1a
Jan 25 00:03:06 kernel: [  186.525974] Write Value: 0x1 to Address: 0xffffff8008f034c0
Jan 25 00:03:06 kernel: [  186.525977] Verification successful at Address: 0xffffff8008f034c0. Value: 0x1
Jan 25 00:03:06 kernel: [  186.525978] Write Value: 0xc0 to Address: 0xffffff8008f0379c
Jan 25 00:03:06 kernel: [  186.525980] Verification successful at Address: 0xffffff8008f0379c. Value: 0xc0
Jan 25 00:03:06 kernel: [  186.525982] Write Value: 0xad to Address: 0xffffff8008f037a0
Jan 25 00:03:06 kernel: [  186.525984] Verification successful at Address: 0xffffff8008f037a0. Value: 0xad
Jan 25 00:03:06 kernel: [  186.525985] Write Value: 0x80 to Address: 0xffffff8008f05018
Jan 25 00:03:06 kernel: [  186.525987] Verification successful at Address: 0xffffff8008f05018. Value: 0x80
Jan 25 00:03:06 kernel: [  186.525997] Write Value: 0x1a to Address: 0xffffff8008f05390
Jan 25 00:03:06 kernel: [  186.525999] Verification successful at Address: 0xffffff8008f05390. Value: 0x1a
Jan 25 00:03:06 kernel: [  186.526001] Write Value: 0x1 to Address: 0xffffff8008f050c0
Jan 25 00:03:06 kernel: [  186.526002] Verification successful at Address: 0xffffff8008f050c0. Value: 0x1
Jan 25 00:03:06 kernel: [  186.526004] Write Value: 0xc0 to Address: 0xffffff8008f0539c
Jan 25 00:03:06 kernel: [  186.526006] Verification successful at Address: 0xffffff8008f0539c. Value: 0xc0
Jan 25 00:03:06 kernel: [  186.526008] Write Value: 0xad to Address: 0xffffff8008f053a0
Jan 25 00:03:06 kernel: [  186.526009] Verification successful at Address: 0xffffff8008f053a0. Value: 0xad
Jan 25 00:03:06 kernel: [  186.526011] Write Value: 0x80 to Address: 0xffffff8008f07418
Jan 25 00:03:06 kernel: [  186.526013] Verification successful at Address: 0xffffff8008f07418. Value: 0x80
Jan 25 00:03:06 kernel: [  186.526014] Write Value: 0x1a to Address: 0xffffff8008f07790
Jan 25 00:03:06 kernel: [  186.526016] Verification successful at Address: 0xffffff8008f07790. Value: 0x1a
Jan 25 00:03:06 kernel: [  186.526017] Write Value: 0x1 to Address: 0xffffff8008f074c0
Jan 25 00:03:06 kernel: [  186.526019] Verification successful at Address: 0xffffff8008f074c0. Value: 0x1
Jan 25 00:03:06 kernel: [  186.526021] Write Value: 0xc0 to Address: 0xffffff8008f0779c
Jan 25 00:03:06 kernel: [  186.526022] Verification successful at Address: 0xffffff8008f0779c. Value: 0xc0
Jan 25 00:03:06 kernel: [  186.526024] Write Value: 0xad to Address: 0xffffff8008f077a0
Jan 25 00:03:06 kernel: [  186.526026] Verification successful at Address: 0xffffff8008f077a0. Value: 0xad
Jan 25 00:03:06 kernel: [  186.527253] Get switch state: 0
Jan 25 00:03:06 kernel: [  186.527340] Get switch state: 0
Jan 25 00:03:06 kernel: [  186.548647] Brightness register values: 00 00
Jan 25 00:03:06 kernel: [  186.548656] Get brightness: 0
Jan 25 00:03:06 kernel: [  186.548739] Brightness register values: 00 00
Jan 25 00:03:06 kernel: [  186.548740] Get brightness: 0
Jan 25 00:03:06 kernel: [  186.549062] Get switch state: 1
Jan 25 00:03:06 kernel: [  186.549138] Get switch state: 1
Jan 25 00:03:06 kernel: [  186.550028] Set white coordinate x:-31, y:-31
Jan 25 00:03:06 kernel: [  186.550652] Set white coordinate x:-31, y:-31
Jan 25 00:03:06 kernel: [  186.594461] Brightness register values: 00 00
Jan 25 00:03:06 kernel: [  186.594470] Get brightness: 0
Jan 25 00:03:06 kernel: [  186.594582] Brightness register values: 00 00
Jan 25 00:03:06 kernel: [  186.594585] Get brightness: 0
Jan 25 00:03:06 kernel: [  186.617544] Brightness register values: 00 32
Jan 25 00:03:06 kernel: [  186.617555] Get brightness: 50
Jan 25 00:03:06 kernel: [  186.617644] Brightness register values: 00 32
Jan 25 00:03:06 kernel: [  186.617645] Get brightness: 50
Jan 25 00:03:06 kernel: [  186.640722] Brightness register values: 00 64
Jan 25 00:03:06 kernel: [  186.640733] Get brightness: 100
Jan 25 00:03:06 kernel: [  186.640847] Brightness register values: 00 64
Jan 25 00:03:06 kernel: [  186.640849] Get brightness: 100
Jan 25 00:03:06 kernel: [  186.665582] Brightness register values: 00 96
Jan 25 00:03:06 kernel: [  186.665596] Get brightness: 150
Jan 25 00:03:06 kernel: [  186.665681] Brightness register values: 00 96
Jan 25 00:03:06 kernel: [  186.665683] Get brightness: 150
Jan 25 00:03:06 kernel: [  186.688817] Brightness register values: 00 c8
Jan 25 00:03:06 kernel: [  186.688830] Get brightness: 200
Jan 25 00:03:06 kernel: [  186.688973] Brightness register values: 00 c8
Jan 25 00:03:06 kernel: [  186.688975] Get brightness: 200
Jan 25 00:03:06 kernel: [  186.711894] Brightness register values: 00 fa
Jan 25 00:03:06 kernel: [  186.711906] Get brightness: 250
Jan 25 00:03:06 kernel: [  186.712040] Brightness register values: 00 fa
Jan 25 00:03:06 kernel: [  186.712043] Get brightness: 250
Jan 25 00:03:06 kernel: [  186.734339] Brightness register values: 01 2c
Jan 25 00:03:06 kernel: [  186.734363] Get brightness: 300
Jan 25 00:03:06 kernel: [  186.734452] Brightness register values: 01 2c
Jan 25 00:03:06 kernel: [  186.734453] Get brightness: 300
Jan 25 00:03:06 kernel: [  186.756558] Brightness register values: 01 5e
Jan 25 00:03:06 kernel: [  186.756602] Get brightness: 350
Jan 25 00:03:06 kernel: [  186.756729] Brightness register values: 01 5e
Jan 25 00:03:06 kernel: [  186.756732] Get brightness: 350
Jan 25 00:03:06 kernel: [  186.777666] Brightness register values: 01 90
Jan 25 00:03:06 kernel: [  186.777677] Get brightness: 400
Jan 25 00:03:06 kernel: [  186.777772] Brightness register values: 01 90
Jan 25 00:03:06 kernel: [  186.777774] Get brightness: 400
Jan 25 00:03:06 kernel: [  186.798549] Brightness register values: 01 c2
Jan 25 00:03:06 kernel: [  186.798561] Get brightness: 450
Jan 25 00:03:06 kernel: [  186.798684] Brightness register values: 01 c2
Jan 25 00:03:06 kernel: [  186.798686] Get brightness: 450
Jan 25 00:04:02 kernel: [  243.419666] Get switch state: 1
Jan 25 00:04:02 kernel: [  243.419766] Get switch state: 1
Jan 25 00:05:47 kernel: [  348.398322] Get switch state: 1
Jan 25 00:05:47 kernel: [  348.398420] Get switch state: 1
Jan 25 00:05:47 kernel: [  348.398999] Brightness register values: 01 f4
Jan 25 00:05:47 kernel: [  348.399005] Get brightness: 500
Jan 25 00:05:47 kernel: [  348.399097] Brightness register values: 01 f4
Jan 25 00:05:47 kernel: [  348.399099] Get brightness: 500
Jan 25 00:05:47 kernel: [  348.438437] Brightness register values: 01 f4
Jan 25 00:05:47 kernel: [  348.438448] Get brightness: 500
Jan 25 00:05:47 kernel: [  348.438650] Brightness register values: 01 f4
Jan 25 00:05:47 kernel: [  348.438652] Get brightness: 500
Jan 25 00:05:48 kernel: [  348.478398] Brightness register values: 01 e0
Jan 25 00:05:48 kernel: [  348.478412] Get brightness: 480
Jan 25 00:05:48 kernel: [  348.478506] Brightness register values: 01 e0
Jan 25 00:05:48 kernel: [  348.478507] Get brightness: 480
Jan 25 00:05:48 kernel: [  348.518360] Brightness register values: 01 cc
Jan 25 00:05:48 kernel: [  348.518377] Get brightness: 460
Jan 25 00:05:48 kernel: [  348.518525] Brightness register values: 01 cc
Jan 25 00:05:48 kernel: [  348.518527] Get brightness: 460
Jan 25 00:05:48 kernel: [  348.558340] Brightness register values: 01 b8
Jan 25 00:05:48 kernel: [  348.558352] Get brightness: 440
Jan 25 00:05:48 kernel: [  348.558443] Brightness register values: 01 b8
Jan 25 00:05:48 kernel: [  348.558445] Get brightness: 440
Jan 25 00:05:48 kernel: [  348.598528] Brightness register values: 01 a4
Jan 25 00:05:48 kernel: [  348.598539] Get brightness: 420
Jan 25 00:05:48 kernel: [  348.598665] Brightness register values: 01 a4
Jan 25 00:05:48 kernel: [  348.598667] Get brightness: 420
Jan 25 00:05:48 kernel: [  348.638370] Brightness register values: 01 90
Jan 25 00:05:48 kernel: [  348.638379] Get brightness: 400
Jan 25 00:05:48 kernel: [  348.638499] Brightness register values: 01 90
Jan 25 00:05:48 kernel: [  348.638503] Get brightness: 400
Jan 25 00:05:48 kernel: [  348.678370] Brightness register values: 01 7c
Jan 25 00:05:48 kernel: [  348.678381] Get brightness: 380
Jan 25 00:05:48 kernel: [  348.678470] Brightness register values: 01 7c
Jan 25 00:05:48 kernel: [  348.678472] Get brightness: 380
Jan 25 00:05:48 kernel: [  348.718432] Brightness register values: 01 68
Jan 25 00:05:48 kernel: [  348.718447] Get brightness: 360
Jan 25 00:05:48 kernel: [  348.718589] Brightness register values: 01 68
Jan 25 00:05:48 kernel: [  348.718593] Get brightness: 360
Jan 25 00:05:48 kernel: [  348.758362] Brightness register values: 01 54
Jan 25 00:05:48 kernel: [  348.758373] Get brightness: 340
Jan 25 00:05:48 kernel: [  348.758468] Brightness register values: 01 54
Jan 25 00:05:48 kernel: [  348.758469] Get brightness: 340
Jan 25 00:05:48 kernel: [  348.798325] Brightness register values: 01 40
Jan 25 00:05:48 kernel: [  348.798338] Get brightness: 320
Jan 25 00:05:48 kernel: [  348.798427] Brightness register values: 01 40
Jan 25 00:05:48 kernel: [  348.798429] Get brightness: 320
Jan 25 00:05:48 kernel: [  348.838536] Brightness register values: 01 2c
Jan 25 00:05:48 kernel: [  348.838546] Get brightness: 300
Jan 25 00:05:48 kernel: [  348.838683] Brightness register values: 01 2c
Jan 25 00:05:48 kernel: [  348.838686] Get brightness: 300
Jan 25 00:05:48 kernel: [  348.878359] Brightness register values: 01 18
Jan 25 00:05:48 kernel: [  348.878369] Get brightness: 280
Jan 25 00:05:48 kernel: [  348.878461] Brightness register values: 01 18
Jan 25 00:05:48 kernel: [  348.878463] Get brightness: 280
Jan 25 00:05:48 kernel: [  348.918325] Brightness register values: 01 04
Jan 25 00:05:48 kernel: [  348.918336] Get brightness: 260
Jan 25 00:05:48 kernel: [  348.918440] Brightness register values: 01 04
Jan 25 00:05:48 kernel: [  348.918442] Get brightness: 260
Jan 25 00:05:48 kernel: [  348.958349] Brightness register values: 00 f0
Jan 25 00:05:48 kernel: [  348.958358] Get brightness: 240
Jan 25 00:05:48 kernel: [  348.958449] Brightness register values: 00 f0
Jan 25 00:05:48 kernel: [  348.958451] Get brightness: 240
Jan 25 00:05:48 kernel: [  348.998307] Brightness register values: 00 dc
Jan 25 00:05:48 kernel: [  348.998318] Get brightness: 220
Jan 25 00:05:48 kernel: [  348.998407] Brightness register values: 00 dc
Jan 25 00:05:48 kernel: [  348.998408] Get brightness: 220
Jan 25 00:05:48 kernel: [  349.038377] Brightness register values: 00 c8
Jan 25 00:05:48 kernel: [  349.038386] Get brightness: 200
Jan 25 00:05:48 kernel: [  349.038527] Brightness register values: 00 c8
Jan 25 00:05:48 kernel: [  349.038530] Get brightness: 200
Jan 25 00:05:48 kernel: [  349.078382] Brightness register values: 00 b4
Jan 25 00:05:48 kernel: [  349.078393] Get brightness: 180
Jan 25 00:05:48 kernel: [  349.078483] Brightness register values: 00 b4
Jan 25 00:05:48 kernel: [  349.078485] Get brightness: 180
Jan 25 00:05:48 kernel: [  349.118348] Brightness register values: 00 a0
Jan 25 00:05:48 kernel: [  349.118359] Get brightness: 160
Jan 25 00:05:48 kernel: [  349.118461] Brightness register values: 00 a0
Jan 25 00:05:48 kernel: [  349.118466] Get brightness: 160
Jan 25 00:05:48 kernel: [  349.158345] Brightness register values: 00 8c
Jan 25 00:05:48 kernel: [  349.158359] Get brightness: 140
Jan 25 00:05:48 kernel: [  349.158453] Brightness register values: 00 8c
Jan 25 00:05:48 kernel: [  349.158455] Get brightness: 140
Jan 25 00:05:48 kernel: [  349.198325] Brightness register values: 00 78
Jan 25 00:05:48 kernel: [  349.198338] Get brightness: 120
Jan 25 00:05:48 kernel: [  349.198432] Brightness register values: 00 78
Jan 25 00:05:48 kernel: [  349.198434] Get brightness: 120
Jan 25 00:05:48 kernel: [  349.238467] Brightness register values: 00 64
Jan 25 00:05:48 kernel: [  349.238479] Get brightness: 100
Jan 25 00:05:48 kernel: [  349.238649] Brightness register values: 00 64
Jan 25 00:05:48 kernel: [  349.238658] Get brightness: 100
Jan 25 00:05:48 kernel: [  349.278366] Brightness register values: 00 50
Jan 25 00:05:48 kernel: [  349.278377] Get brightness: 80
Jan 25 00:05:48 kernel: [  349.278473] Brightness register values: 00 50
Jan 25 00:05:48 kernel: [  349.278474] Get brightness: 80
Jan 25 00:05:48 kernel: [  349.318375] Brightness register values: 00 3c
Jan 25 00:05:48 kernel: [  349.318386] Get brightness: 60
Jan 25 00:05:48 kernel: [  349.318499] Brightness register values: 00 3c
Jan 25 00:05:48 kernel: [  349.318502] Get brightness: 60
Jan 25 00:05:48 kernel: [  349.358352] Brightness register values: 00 28
Jan 25 00:05:48 kernel: [  349.358363] Get brightness: 40
Jan 25 00:05:48 kernel: [  349.358459] Brightness register values: 00 28
Jan 25 00:05:48 kernel: [  349.358460] Get brightness: 40
Jan 25 00:05:48 kernel: [  349.398339] Brightness register values: 00 14
Jan 25 00:05:48 kernel: [  349.398348] Get brightness: 20
Jan 25 00:05:48 kernel: [  349.398437] Brightness register values: 00 14
Jan 25 00:05:48 kernel: [  349.398438] Get brightness: 20
Jan 25 00:08:18 kernel: [  498.596649] Get switch state: 0
Jan 25 00:08:18 kernel: [  498.596761] Get switch state: 0
Jan 25 00:08:18 kernel: [  498.619596] Brightness register values: 00 00
Jan 25 00:08:18 kernel: [  498.619608] Get brightness: 0
Jan 25 00:08:18 kernel: [  498.619736] Brightness register values: 00 00
Jan 25 00:08:18 kernel: [  498.619738] Get brightness: 0
Jan 25 00:08:18 kernel: [  498.641340] Brightness register values: 00 00
Jan 25 00:08:18 kernel: [  498.641348] Get brightness: 0
Jan 25 00:08:18 kernel: [  498.641443] Brightness register values: 00 00
Jan 25 00:08:18 kernel: [  498.641445] Get brightness: 0
Jan 25 00:08:18 kernel: [  498.678510] Brightness register values: 00 32
Jan 25 00:08:18 kernel: [  498.678524] Get brightness: 50
Jan 25 00:08:18 kernel: [  498.678653] Brightness register values: 00 32
Jan 25 00:08:18 kernel: [  498.678662] Get brightness: 50
Jan 25 00:08:18 kernel: [  498.718389] Brightness register values: 00 64
Jan 25 00:08:18 kernel: [  498.718398] Get brightness: 100
Jan 25 00:08:18 kernel: [  498.718493] Brightness register values: 00 64
Jan 25 00:08:18 kernel: [  498.718495] Get brightness: 100
Jan 25 00:08:18 kernel: [  498.758351] Brightness register values: 00 96
Jan 25 00:08:18 kernel: [  498.758362] Get brightness: 150
Jan 25 00:08:18 kernel: [  498.758456] Brightness register values: 00 96
Jan 25 00:08:18 kernel: [  498.758458] Get brightness: 150
Jan 25 00:08:18 kernel: [  498.798377] Brightness register values: 00 c8
Jan 25 00:08:18 kernel: [  498.798390] Get brightness: 200
Jan 25 00:08:18 kernel: [  498.798485] Brightness register values: 00 c8
Jan 25 00:08:18 kernel: [  498.798487] Get brightness: 200
Jan 25 00:08:18 kernel: [  498.838382] Brightness register values: 00 fa
Jan 25 00:08:18 kernel: [  498.838392] Get brightness: 250
Jan 25 00:08:18 kernel: [  498.838478] Brightness register values: 00 fa
Jan 25 00:08:18 kernel: [  498.838480] Get brightness: 250
Jan 25 00:08:18 kernel: [  498.878374] Brightness register values: 01 2c
Jan 25 00:08:18 kernel: [  498.878382] Get brightness: 300
Jan 25 00:08:18 kernel: [  498.878473] Brightness register values: 01 2c
Jan 25 00:08:18 kernel: [  498.878475] Get brightness: 300
Jan 25 00:08:18 kernel: [  498.918340] Brightness register values: 01 5e
Jan 25 00:08:18 kernel: [  498.918349] Get brightness: 350
Jan 25 00:08:18 kernel: [  498.918441] Brightness register values: 01 5e
Jan 25 00:08:18 kernel: [  498.918443] Get brightness: 350
Jan 25 00:08:18 kernel: [  498.958350] Brightness register values: 01 90
Jan 25 00:08:18 kernel: [  498.958363] Get brightness: 400
Jan 25 00:08:18 kernel: [  498.958452] Brightness register values: 01 90
Jan 25 00:08:18 kernel: [  498.958454] Get brightness: 400
Jan 25 00:08:18 kernel: [  498.998401] Brightness register values: 01 c2
Jan 25 00:08:18 kernel: [  498.998414] Get brightness: 450
Jan 25 00:08:18 kernel: [  498.998507] Brightness register values: 01 c2
Jan 25 00:08:18 kernel: [  498.998509] Get brightness: 450
Jan 25 00:08:35 kernel: [  516.393609] Get switch state: 1
Jan 25 00:08:35 kernel: [  516.393735] Get switch state: 1
Jan 25 00:08:35 kernel: [  516.394562] Brightness register values: 01 f4
Jan 25 00:08:35 kernel: [  516.394588] Get brightness: 500
Jan 25 00:08:35 kernel: [  516.394710] Brightness register values: 01 f4
Jan 25 00:08:35 kernel: [  516.394712] Get brightness: 500
Jan 25 00:08:35 kernel: [  516.418356] Brightness register values: 01 f4
Jan 25 00:08:35 kernel: [  516.418367] Get brightness: 500
Jan 25 00:08:35 kernel: [  516.418460] Brightness register values: 01 f4
Jan 25 00:08:35 kernel: [  516.418462] Get brightness: 500
Jan 25 00:08:36 kernel: [  516.458395] Brightness register values: 01 e0
Jan 25 00:08:36 kernel: [  516.458404] Get brightness: 480
Jan 25 00:08:36 kernel: [  516.458500] Brightness register values: 01 e0
Jan 25 00:08:36 kernel: [  516.458502] Get brightness: 480
Jan 25 00:08:36 kernel: [  516.498338] Brightness register values: 01 cc
Jan 25 00:08:36 kernel: [  516.498350] Get brightness: 460
Jan 25 00:08:36 kernel: [  516.498445] Brightness register values: 01 cc
Jan 25 00:08:36 kernel: [  516.498447] Get brightness: 460
Jan 25 00:08:36 kernel: [  516.538397] Brightness register values: 01 b8
Jan 25 00:08:36 kernel: [  516.538411] Get brightness: 440
Jan 25 00:08:36 kernel: [  516.538501] Brightness register values: 01 b8
Jan 25 00:08:36 kernel: [  516.538503] Get brightness: 440
Jan 25 00:08:36 kernel: [  516.578364] Brightness register values: 01 a4
Jan 25 00:08:36 kernel: [  516.578374] Get brightness: 420
Jan 25 00:08:36 kernel: [  516.578463] Brightness register values: 01 a4
Jan 25 00:08:36 kernel: [  516.578464] Get brightness: 420
Jan 25 00:08:36 kernel: [  516.618359] Brightness register values: 01 90
Jan 25 00:08:36 kernel: [  516.618368] Get brightness: 400
Jan 25 00:08:36 kernel: [  516.618461] Brightness register values: 01 90
Jan 25 00:08:36 kernel: [  516.618462] Get brightness: 400
Jan 25 00:08:36 kernel: [  516.658343] Brightness register values: 01 7c
Jan 25 00:08:36 kernel: [  516.658355] Get brightness: 380
Jan 25 00:08:36 kernel: [  516.658447] Brightness register values: 01 7c
Jan 25 00:08:36 kernel: [  516.658449] Get brightness: 380
Jan 25 00:08:36 kernel: [  516.698427] Brightness register values: 01 68
Jan 25 00:08:36 kernel: [  516.698437] Get brightness: 360
Jan 25 00:08:36 kernel: [  516.698530] Brightness register values: 01 68
Jan 25 00:08:36 kernel: [  516.698532] Get brightness: 360
Jan 25 00:08:36 kernel: [  516.738458] Brightness register values: 01 54
Jan 25 00:08:36 kernel: [  516.738468] Get brightness: 340
Jan 25 00:08:36 kernel: [  516.738561] Brightness register values: 01 54
Jan 25 00:08:36 kernel: [  516.738562] Get brightness: 340
Jan 25 00:08:36 kernel: [  516.778351] Brightness register values: 01 40
Jan 25 00:08:36 kernel: [  516.778362] Get brightness: 320
Jan 25 00:08:36 kernel: [  516.778521] Brightness register values: 01 40
Jan 25 00:08:36 kernel: [  516.778522] Get brightness: 320
Jan 25 00:08:36 kernel: [  516.818349] Brightness register values: 01 2c
Jan 25 00:08:36 kernel: [  516.818360] Get brightness: 300
Jan 25 00:08:36 kernel: [  516.818455] Brightness register values: 01 2c
Jan 25 00:08:36 kernel: [  516.818458] Get brightness: 300
Jan 25 00:08:36 kernel: [  516.858458] Brightness register values: 01 18
Jan 25 00:08:36 kernel: [  516.858469] Get brightness: 280
Jan 25 00:08:36 kernel: [  516.858562] Brightness register values: 01 18
Jan 25 00:08:36 kernel: [  516.858564] Get brightness: 280
Jan 25 00:08:36 kernel: [  516.898335] Brightness register values: 01 04
Jan 25 00:08:36 kernel: [  516.898344] Get brightness: 260
Jan 25 00:08:36 kernel: [  516.898429] Brightness register values: 01 04
Jan 25 00:08:36 kernel: [  516.898431] Get brightness: 260
Jan 25 00:08:36 kernel: [  516.938423] Brightness register values: 00 f0
Jan 25 00:08:36 kernel: [  516.938434] Get brightness: 240
Jan 25 00:08:36 kernel: [  516.938530] Brightness register values: 00 f0
Jan 25 00:08:36 kernel: [  516.938539] Get brightness: 240
Jan 25 00:08:36 kernel: [  516.978377] Brightness register values: 00 dc
Jan 25 00:08:36 kernel: [  516.978386] Get brightness: 220
Jan 25 00:08:36 kernel: [  516.978473] Brightness register values: 00 dc
Jan 25 00:08:36 kernel: [  516.978474] Get brightness: 220
Jan 25 00:08:36 kernel: [  517.018769] Brightness register values: 00 c8
Jan 25 00:08:36 kernel: [  517.018779] Get brightness: 200
Jan 25 00:08:36 kernel: [  517.018881] Brightness register values: 00 c8
Jan 25 00:08:36 kernel: [  517.018883] Get brightness: 200
Jan 25 00:08:36 kernel: [  517.058366] Brightness register values: 00 b4
Jan 25 00:08:36 kernel: [  517.058377] Get brightness: 180
Jan 25 00:08:36 kernel: [  517.058466] Brightness register values: 00 b4
Jan 25 00:08:36 kernel: [  517.058467] Get brightness: 180
Jan 25 00:08:36 kernel: [  517.098359] Brightness register values: 00 a0
Jan 25 00:08:36 kernel: [  517.098369] Get brightness: 160
Jan 25 00:08:36 kernel: [  517.098459] Brightness register values: 00 a0
Jan 25 00:08:36 kernel: [  517.098461] Get brightness: 160
Jan 25 00:08:36 kernel: [  517.138395] Brightness register values: 00 8c
Jan 25 00:08:36 kernel: [  517.138407] Get brightness: 140
Jan 25 00:08:36 kernel: [  517.138502] Brightness register values: 00 8c
Jan 25 00:08:36 kernel: [  517.138504] Get brightness: 140
Jan 25 00:08:36 kernel: [  517.178443] Brightness register values: 00 78
Jan 25 00:08:36 kernel: [  517.178455] Get brightness: 120
Jan 25 00:08:36 kernel: [  517.178573] Brightness register values: 00 78
Jan 25 00:08:36 kernel: [  517.178576] Get brightness: 120
Jan 25 00:08:36 kernel: [  517.218348] Brightness register values: 00 64
Jan 25 00:08:36 kernel: [  517.218357] Get brightness: 100
Jan 25 00:08:36 kernel: [  517.218457] Brightness register values: 00 64
Jan 25 00:08:36 kernel: [  517.218459] Get brightness: 100
Jan 25 00:08:36 kernel: [  517.258327] Brightness register values: 00 50
Jan 25 00:08:36 kernel: [  517.258345] Get brightness: 80
Jan 25 00:08:36 kernel: [  517.258463] Brightness register values: 00 50
Jan 25 00:08:36 kernel: [  517.258466] Get brightness: 80
Jan 25 00:08:36 kernel: [  517.298306] Brightness register values: 00 3c
Jan 25 00:08:36 kernel: [  517.298317] Get brightness: 60
Jan 25 00:08:36 kernel: [  517.298410] Brightness register values: 00 3c
Jan 25 00:08:36 kernel: [  517.298411] Get brightness: 60
Jan 25 00:08:36 kernel: [  517.338430] Brightness register values: 00 28
Jan 25 00:08:36 kernel: [  517.338442] Get brightness: 40
Jan 25 00:08:36 kernel: [  517.338532] Brightness register values: 00 28
Jan 25 00:08:36 kernel: [  517.338534] Get brightness: 40
Jan 25 00:08:36 kernel: [  517.378355] Brightness register values: 00 14
Jan 25 00:08:36 kernel: [  517.378365] Get brightness: 20
Jan 25 00:08:36 kernel: [  517.378459] Brightness register values: 00 14
Jan 25 00:08:36 kernel: [  517.378460] Get brightness: 20
Jan 25 00:08:39 kernel: [  519.561939] Get switch state: 0
Jan 25 00:08:39 kernel: [  519.562041] Get switch state: 0
Jan 25 00:08:39 kernel: [  519.584104] Brightness register values: 00 00
Jan 25 00:08:39 kernel: [  519.584111] Get brightness: 0
Jan 25 00:08:39 kernel: [  519.584204] Brightness register values: 00 00
Jan 25 00:08:39 kernel: [  519.584206] Get brightness: 0
Jan 25 00:08:39 kernel: [  519.618427] Brightness register values: 00 00
Jan 25 00:08:39 kernel: [  519.618439] Get brightness: 0
Jan 25 00:08:39 kernel: [  519.618531] Brightness register values: 00 00
Jan 25 00:08:39 kernel: [  519.618533] Get brightness: 0
Jan 25 00:08:39 kernel: [  519.658366] Brightness register values: 00 32
Jan 25 00:08:39 kernel: [  519.658375] Get brightness: 50
Jan 25 00:08:39 kernel: [  519.658496] Brightness register values: 00 32
Jan 25 00:08:39 kernel: [  519.658499] Get brightness: 50
Jan 25 00:08:39 kernel: [  519.698337] Brightness register values: 00 64
Jan 25 00:08:39 kernel: [  519.698348] Get brightness: 100
Jan 25 00:08:39 kernel: [  519.698445] Brightness register values: 00 64
Jan 25 00:08:39 kernel: [  519.698448] Get brightness: 100
Jan 25 00:08:39 kernel: [  519.738447] Brightness register values: 00 96
Jan 25 00:08:39 kernel: [  519.738457] Get brightness: 150
Jan 25 00:08:39 kernel: [  519.738543] Brightness register values: 00 96
Jan 25 00:08:39 kernel: [  519.738545] Get brightness: 150
Jan 25 00:08:39 kernel: [  519.778410] Brightness register values: 00 c8
Jan 25 00:08:39 kernel: [  519.778423] Get brightness: 200
Jan 25 00:08:39 kernel: [  519.778513] Brightness register values: 00 c8
Jan 25 00:08:39 kernel: [  519.778514] Get brightness: 200
Jan 25 00:08:39 kernel: [  519.818379] Brightness register values: 00 fa
Jan 25 00:08:39 kernel: [  519.818389] Get brightness: 250
Jan 25 00:08:39 kernel: [  519.818479] Brightness register values: 00 fa
Jan 25 00:08:39 kernel: [  519.818481] Get brightness: 250
Jan 25 00:08:39 kernel: [  519.858343] Brightness register values: 01 2c
Jan 25 00:08:39 kernel: [  519.858356] Get brightness: 300
Jan 25 00:08:39 kernel: [  519.858449] Brightness register values: 01 2c
Jan 25 00:08:39 kernel: [  519.858451] Get brightness: 300
Jan 25 00:08:39 kernel: [  519.898386] Brightness register values: 01 5e
Jan 25 00:08:39 kernel: [  519.898395] Get brightness: 350
Jan 25 00:08:39 kernel: [  519.898493] Brightness register values: 01 5e
Jan 25 00:08:39 kernel: [  519.898495] Get brightness: 350
Jan 25 00:08:39 kernel: [  519.938458] Brightness register values: 01 90
Jan 25 00:08:39 kernel: [  519.938472] Get brightness: 400
Jan 25 00:08:39 kernel: [  519.938597] Brightness register values: 01 90
Jan 25 00:08:39 kernel: [  519.938600] Get brightness: 400
Jan 25 00:08:39 kernel: [  519.978343] Brightness register values: 01 c2
Jan 25 00:08:39 kernel: [  519.978354] Get brightness: 450
Jan 25 00:08:39 kernel: [  519.978440] Brightness register values: 01 c2
Jan 25 00:08:39 kernel: [  519.978442] Get brightness: 450
Jan 25 00:08:39 kernel: [  520.430268] Get switch state: 1
Jan 25 00:08:39 kernel: [  520.430354] Get switch state: 1
Jan 25 00:09:11 kernel: [  551.980276] Get switch state: 1
Jan 25 00:09:11 kernel: [  551.980369] Get switch state: 1
Jan 25 00:10:04 kernel: [  605.180477] Get switch state: 1
Jan 25 00:10:04 kernel: [  605.180565] Get switch state: 1
Jan 25 00:10:04 kernel: [  605.180917] Brightness register values: 01 f4
Jan 25 00:10:04 kernel: [  605.180923] Get brightness: 500
Jan 25 00:10:04 kernel: [  605.181023] Brightness register values: 01 f4
Jan 25 00:10:04 kernel: [  605.181025] Get brightness: 500
Jan 25 00:10:04 kernel: [  605.218393] Brightness register values: 01 f4
Jan 25 00:10:04 kernel: [  605.218402] Get brightness: 500
Jan 25 00:10:04 kernel: [  605.218501] Brightness register values: 01 f4
Jan 25 00:10:04 kernel: [  605.218503] Get brightness: 500
Jan 25 00:10:04 kernel: [  605.258434] Brightness register values: 01 e0
Jan 25 00:10:04 kernel: [  605.258443] Get brightness: 480
Jan 25 00:10:04 kernel: [  605.258543] Brightness register values: 01 e0
Jan 25 00:10:04 kernel: [  605.258545] Get brightness: 480
Jan 25 00:10:04 kernel: [  605.298345] Brightness register values: 01 cc
Jan 25 00:10:04 kernel: [  605.298355] Get brightness: 460
Jan 25 00:10:04 kernel: [  605.298446] Brightness register values: 01 cc
Jan 25 00:10:04 kernel: [  605.298448] Get brightness: 460
Jan 25 00:10:04 kernel: [  605.338324] Brightness register values: 01 b8
Jan 25 00:10:04 kernel: [  605.338344] Get brightness: 440
Jan 25 00:10:04 kernel: [  605.338488] Brightness register values: 01 b8
Jan 25 00:10:04 kernel: [  605.338491] Get brightness: 440
Jan 25 00:10:04 kernel: [  605.378384] Brightness register values: 01 a4
Jan 25 00:10:04 kernel: [  605.378394] Get brightness: 420
Jan 25 00:10:04 kernel: [  605.378483] Brightness register values: 01 a4
Jan 25 00:10:04 kernel: [  605.378484] Get brightness: 420
Jan 25 00:10:04 kernel: [  605.418382] Brightness register values: 01 90
Jan 25 00:10:04 kernel: [  605.418394] Get brightness: 400
Jan 25 00:10:04 kernel: [  605.418480] Brightness register values: 01 90
Jan 25 00:10:04 kernel: [  605.418482] Get brightness: 400
Jan 25 00:10:05 kernel: [  605.458339] Brightness register values: 01 7c
Jan 25 00:10:05 kernel: [  605.458349] Get brightness: 380
Jan 25 00:10:05 kernel: [  605.458436] Brightness register values: 01 7c
Jan 25 00:10:05 kernel: [  605.458438] Get brightness: 380
Jan 25 00:10:05 kernel: [  605.498333] Brightness register values: 01 68
Jan 25 00:10:05 kernel: [  605.498344] Get brightness: 360
Jan 25 00:10:05 kernel: [  605.498431] Brightness register values: 01 68
Jan 25 00:10:05 kernel: [  605.498432] Get brightness: 360
Jan 25 00:10:05 kernel: [  605.538383] Brightness register values: 01 54
Jan 25 00:10:05 kernel: [  605.538392] Get brightness: 340
Jan 25 00:10:05 kernel: [  605.538483] Brightness register values: 01 54
Jan 25 00:10:05 kernel: [  605.538485] Get brightness: 340
Jan 25 00:10:05 kernel: [  605.578451] Brightness register values: 01 40
Jan 25 00:10:05 kernel: [  605.578460] Get brightness: 320
Jan 25 00:10:05 kernel: [  605.578554] Brightness register values: 01 40
Jan 25 00:10:05 kernel: [  605.578556] Get brightness: 320
Jan 25 00:10:05 kernel: [  605.618344] Brightness register values: 01 2c
Jan 25 00:10:05 kernel: [  605.618353] Get brightness: 300
Jan 25 00:10:05 kernel: [  605.618445] Brightness register values: 01 2c
Jan 25 00:10:05 kernel: [  605.618447] Get brightness: 300
Jan 25 00:10:05 kernel: [  605.658362] Brightness register values: 01 18
Jan 25 00:10:05 kernel: [  605.658373] Get brightness: 280
Jan 25 00:10:05 kernel: [  605.658475] Brightness register values: 01 18
Jan 25 00:10:05 kernel: [  605.658477] Get brightness: 280
Jan 25 00:10:05 kernel: [  605.698334] Brightness register values: 01 04
Jan 25 00:10:05 kernel: [  605.698346] Get brightness: 260
Jan 25 00:10:05 kernel: [  605.698441] Brightness register values: 01 04
Jan 25 00:10:05 kernel: [  605.698443] Get brightness: 260
Jan 25 00:10:05 kernel: [  605.738317] Brightness register values: 00 f0
Jan 25 00:10:05 kernel: [  605.738327] Get brightness: 240
Jan 25 00:10:05 kernel: [  605.738414] Brightness register values: 00 f0
Jan 25 00:10:05 kernel: [  605.738415] Get brightness: 240
Jan 25 00:10:05 kernel: [  605.778377] Brightness register values: 00 dc
Jan 25 00:10:05 kernel: [  605.778387] Get brightness: 220
Jan 25 00:10:05 kernel: [  605.778473] Brightness register values: 00 dc
Jan 25 00:10:05 kernel: [  605.778475] Get brightness: 220
Jan 25 00:10:05 kernel: [  605.818380] Brightness register values: 00 c8
Jan 25 00:10:05 kernel: [  605.818388] Get brightness: 200
Jan 25 00:10:05 kernel: [  605.818483] Brightness register values: 00 c8
Jan 25 00:10:05 kernel: [  605.818485] Get brightness: 200
Jan 25 00:10:05 kernel: [  605.858386] Brightness register values: 00 b4
Jan 25 00:10:05 kernel: [  605.858397] Get brightness: 180
Jan 25 00:10:05 kernel: [  605.858482] Brightness register values: 00 b4
Jan 25 00:10:05 kernel: [  605.858486] Get brightness: 180
Jan 25 00:10:05 kernel: [  605.898337] Brightness register values: 00 a0
Jan 25 00:10:05 kernel: [  605.898347] Get brightness: 160
Jan 25 00:10:05 kernel: [  605.898434] Brightness register values: 00 a0
Jan 25 00:10:05 kernel: [  605.898435] Get brightness: 160
Jan 25 00:10:05 kernel: [  605.938326] Brightness register values: 00 8c
Jan 25 00:10:05 kernel: [  605.938344] Get brightness: 140
Jan 25 00:10:05 kernel: [  605.938459] Brightness register values: 00 8c
Jan 25 00:10:05 kernel: [  605.938462] Get brightness: 140
Jan 25 00:10:05 kernel: [  605.978453] Brightness register values: 00 78
Jan 25 00:10:05 kernel: [  605.978464] Get brightness: 120
Jan 25 00:10:05 kernel: [  605.978557] Brightness register values: 00 78
Jan 25 00:10:05 kernel: [  605.978559] Get brightness: 120
Jan 25 00:10:05 kernel: [  606.018384] Brightness register values: 00 64
Jan 25 00:10:05 kernel: [  606.018393] Get brightness: 100
Jan 25 00:10:05 kernel: [  606.018486] Brightness register values: 00 64
Jan 25 00:10:05 kernel: [  606.018487] Get brightness: 100
Jan 25 00:10:05 kernel: [  606.058364] Brightness register values: 00 50
Jan 25 00:10:05 kernel: [  606.058378] Get brightness: 80
Jan 25 00:10:05 kernel: [  606.058472] Brightness register values: 00 50
Jan 25 00:10:05 kernel: [  606.058474] Get brightness: 80
Jan 25 00:10:05 kernel: [  606.098344] Brightness register values: 00 3c
Jan 25 00:10:05 kernel: [  606.098359] Get brightness: 60
Jan 25 00:10:05 kernel: [  606.098445] Brightness register values: 00 3c
Jan 25 00:10:05 kernel: [  606.098447] Get brightness: 60
Jan 25 00:10:05 kernel: [  606.138322] Brightness register values: 00 28
Jan 25 00:10:05 kernel: [  606.138332] Get brightness: 40
Jan 25 00:10:05 kernel: [  606.138427] Brightness register values: 00 28
Jan 25 00:10:05 kernel: [  606.138429] Get brightness: 40
Jan 25 00:10:05 kernel: [  606.178407] Brightness register values: 00 14
Jan 25 00:10:05 kernel: [  606.178415] Get brightness: 20
Jan 25 00:10:05 kernel: [  606.178509] Brightness register values: 00 14
Jan 25 00:10:05 kernel: [  606.178511] Get brightness: 20
Jan 25 00:11:28 kernel: [  689.363323] Get switch state: 0
Jan 25 00:11:28 kernel: [  689.363413] Get switch state: 0
Jan 25 00:11:28 kernel: [  689.385292] Brightness register values: 00 00
Jan 25 00:11:28 kernel: [  689.385303] Get brightness: 0
Jan 25 00:11:28 kernel: [  689.385400] Brightness register values: 00 00
Jan 25 00:11:28 kernel: [  689.385401] Get brightness: 0
Jan 25 00:11:28 kernel: [  689.418358] Brightness register values: 00 00
Jan 25 00:11:28 kernel: [  689.418369] Get brightness: 0
Jan 25 00:11:28 kernel: [  689.418456] Brightness register values: 00 00
Jan 25 00:11:28 kernel: [  689.418458] Get brightness: 0
Jan 25 00:11:29 kernel: [  689.458334] Brightness register values: 00 32
Jan 25 00:11:29 kernel: [  689.458344] Get brightness: 50
Jan 25 00:11:29 kernel: [  689.458436] Brightness register values: 00 32
Jan 25 00:11:29 kernel: [  689.458438] Get brightness: 50
Jan 25 00:11:29 kernel: [  689.498338] Brightness register values: 00 64
Jan 25 00:11:29 kernel: [  689.498348] Get brightness: 100
Jan 25 00:11:29 kernel: [  689.498481] Brightness register values: 00 64
Jan 25 00:11:29 kernel: [  689.498485] Get brightness: 100
Jan 25 00:11:29 kernel: [  689.538346] Brightness register values: 00 96
Jan 25 00:11:29 kernel: [  689.538361] Get brightness: 150
Jan 25 00:11:29 kernel: [  689.538465] Brightness register values: 00 96
Jan 25 00:11:29 kernel: [  689.538467] Get brightness: 150
Jan 25 00:11:29 kernel: [  689.578338] Brightness register values: 00 c8
Jan 25 00:11:29 kernel: [  689.578350] Get brightness: 200
Jan 25 00:11:29 kernel: [  689.578485] Brightness register values: 00 c8
Jan 25 00:11:29 kernel: [  689.578497] Get brightness: 200
Jan 25 00:11:29 kernel: [  689.618651] Brightness register values: 00 fa
Jan 25 00:11:29 kernel: [  689.618661] Get brightness: 250
Jan 25 00:11:29 kernel: [  689.618753] Brightness register values: 00 fa
Jan 25 00:11:29 kernel: [  689.618755] Get brightness: 250
Jan 25 00:11:29 kernel: [  689.658348] Brightness register values: 01 2c
Jan 25 00:11:29 kernel: [  689.658356] Get brightness: 300
Jan 25 00:11:29 kernel: [  689.658456] Brightness register values: 01 2c
Jan 25 00:11:29 kernel: [  689.658458] Get brightness: 300
Jan 25 00:11:29 kernel: [  689.698415] Brightness register values: 01 5e
Jan 25 00:11:29 kernel: [  689.698425] Get brightness: 350
Jan 25 00:11:29 kernel: [  689.698527] Brightness register values: 01 5e
Jan 25 00:11:29 kernel: [  689.698528] Get brightness: 350
Jan 25 00:11:29 kernel: [  689.738322] Brightness register values: 01 90
Jan 25 00:11:29 kernel: [  689.738331] Get brightness: 400
Jan 25 00:11:29 kernel: [  689.738427] Brightness register values: 01 90
Jan 25 00:11:29 kernel: [  689.738428] Get brightness: 400
Jan 25 00:11:29 kernel: [  689.778414] Brightness register values: 01 c2
Jan 25 00:11:29 kernel: [  689.778425] Get brightness: 450
Jan 25 00:11:29 kernel: [  689.778602] Brightness register values: 01 c2
Jan 25 00:11:29 kernel: [  689.778609] Get brightness: 450
Jan 25 00:11:44 kernel: [  705.201363] Get switch state: 1
Jan 25 00:11:44 kernel: [  705.201447] Get switch state: 1
Jan 25 00:11:44 kernel: [  705.201946] Brightness register values: 01 f4
Jan 25 00:11:44 kernel: [  705.201951] Get brightness: 500
Jan 25 00:11:44 kernel: [  705.202045] Brightness register values: 01 f4
Jan 25 00:11:44 kernel: [  705.202047] Get brightness: 500
Jan 25 00:11:44 kernel: [  705.238323] Brightness register values: 01 f4
Jan 25 00:11:44 kernel: [  705.238334] Get brightness: 500
Jan 25 00:11:44 kernel: [  705.238433] Brightness register values: 01 f4
Jan 25 00:11:44 kernel: [  705.238436] Get brightness: 500
Jan 25 00:11:44 kernel: [  705.278418] Brightness register values: 01 e0
Jan 25 00:11:44 kernel: [  705.278428] Get brightness: 480
Jan 25 00:11:44 kernel: [  705.278567] Brightness register values: 01 e0
Jan 25 00:11:44 kernel: [  705.278569] Get brightness: 480
Jan 25 00:11:44 kernel: [  705.318359] Brightness register values: 01 cc
Jan 25 00:11:44 kernel: [  705.318370] Get brightness: 460
Jan 25 00:11:44 kernel: [  705.318461] Brightness register values: 01 cc
Jan 25 00:11:44 kernel: [  705.318463] Get brightness: 460
Jan 25 00:11:44 kernel: [  705.358394] Brightness register values: 01 b8
Jan 25 00:11:44 kernel: [  705.358404] Get brightness: 440
Jan 25 00:11:44 kernel: [  705.358532] Brightness register values: 01 b8
Jan 25 00:11:44 kernel: [  705.358537] Get brightness: 440
Jan 25 00:11:44 kernel: [  705.398367] Brightness register values: 01 a4
Jan 25 00:11:44 kernel: [  705.398376] Get brightness: 420
Jan 25 00:11:44 kernel: [  705.398471] Brightness register values: 01 a4
Jan 25 00:11:44 kernel: [  705.398473] Get brightness: 420
Jan 25 00:11:44 kernel: [  705.438329] Brightness register values: 01 90
Jan 25 00:11:44 kernel: [  705.438343] Get brightness: 400
Jan 25 00:11:44 kernel: [  705.438438] Brightness register values: 01 90
Jan 25 00:11:44 kernel: [  705.438440] Get brightness: 400
Jan 25 00:11:45 kernel: [  705.478491] Brightness register values: 01 7c
Jan 25 00:11:45 kernel: [  705.478512] Get brightness: 380
Jan 25 00:11:45 kernel: [  705.478725] Brightness register values: 01 7c
Jan 25 00:11:45 kernel: [  705.478729] Get brightness: 380
Jan 25 00:11:45 kernel: [  705.518469] Brightness register values: 01 68
Jan 25 00:11:45 kernel: [  705.518481] Get brightness: 360
Jan 25 00:11:45 kernel: [  705.518587] Brightness register values: 01 68
Jan 25 00:11:45 kernel: [  705.518589] Get brightness: 360
Jan 25 00:11:45 kernel: [  705.558430] Brightness register values: 01 54
Jan 25 00:11:45 kernel: [  705.558445] Get brightness: 340
Jan 25 00:11:45 kernel: [  705.558627] Brightness register values: 01 54
Jan 25 00:11:45 kernel: [  705.558631] Get brightness: 340
Jan 25 00:11:45 kernel: [  705.598334] Brightness register values: 01 40
Jan 25 00:11:45 kernel: [  705.598343] Get brightness: 320
Jan 25 00:11:45 kernel: [  705.598445] Brightness register values: 01 40
Jan 25 00:11:45 kernel: [  705.598446] Get brightness: 320
Jan 25 00:11:45 kernel: [  705.638337] Brightness register values: 01 2c
Jan 25 00:11:45 kernel: [  705.638348] Get brightness: 300
Jan 25 00:11:45 kernel: [  705.638447] Brightness register values: 01 2c
Jan 25 00:11:45 kernel: [  705.638449] Get brightness: 300
Jan 25 00:11:45 kernel: [  705.678348] Brightness register values: 01 18
Jan 25 00:11:45 kernel: [  705.678361] Get brightness: 280
Jan 25 00:11:45 kernel: [  705.678473] Brightness register values: 01 18
Jan 25 00:11:45 kernel: [  705.678478] Get brightness: 280
Jan 25 00:11:45 kernel: [  705.718336] Brightness register values: 01 04
Jan 25 00:11:45 kernel: [  705.718349] Get brightness: 260
Jan 25 00:11:45 kernel: [  705.718464] Brightness register values: 01 04
Jan 25 00:11:45 kernel: [  705.718469] Get brightness: 260
Jan 25 00:11:45 kernel: [  705.758355] Brightness register values: 00 f0
Jan 25 00:11:45 kernel: [  705.758367] Get brightness: 240
Jan 25 00:11:45 kernel: [  705.758555] Brightness register values: 00 f0
Jan 25 00:11:45 kernel: [  705.758557] Get brightness: 240
Jan 25 00:11:45 kernel: [  705.798337] Brightness register values: 00 dc
Jan 25 00:11:45 kernel: [  705.798350] Get brightness: 220
Jan 25 00:11:45 kernel: [  705.798444] Brightness register values: 00 dc
Jan 25 00:11:45 kernel: [  705.798446] Get brightness: 220
Jan 25 00:11:45 kernel: [  705.838324] Brightness register values: 00 c8
Jan 25 00:11:45 kernel: [  705.838334] Get brightness: 200
Jan 25 00:11:45 kernel: [  705.838427] Brightness register values: 00 c8
Jan 25 00:11:45 kernel: [  705.838429] Get brightness: 200
Jan 25 00:11:45 kernel: [  705.878393] Brightness register values: 00 b4
Jan 25 00:11:45 kernel: [  705.878401] Get brightness: 180
Jan 25 00:11:45 kernel: [  705.878518] Brightness register values: 00 b4
Jan 25 00:11:45 kernel: [  705.878521] Get brightness: 180
Jan 25 00:11:45 kernel: [  705.918436] Brightness register values: 00 a0
Jan 25 00:11:45 kernel: [  705.918445] Get brightness: 160
Jan 25 00:11:45 kernel: [  705.918532] Brightness register values: 00 a0
Jan 25 00:11:45 kernel: [  705.918533] Get brightness: 160
Jan 25 00:11:45 kernel: [  705.958413] Brightness register values: 00 8c
Jan 25 00:11:45 kernel: [  705.958422] Get brightness: 140
Jan 25 00:11:45 kernel: [  705.958563] Brightness register values: 00 8c
Jan 25 00:11:45 kernel: [  705.958567] Get brightness: 140
Jan 25 00:11:45 kernel: [  705.998338] Brightness register values: 00 78
Jan 25 00:11:45 kernel: [  705.998349] Get brightness: 120
Jan 25 00:11:45 kernel: [  705.998440] Brightness register values: 00 78
Jan 25 00:11:45 kernel: [  705.998442] Get brightness: 120
Jan 25 00:11:45 kernel: [  706.038328] Brightness register values: 00 64
Jan 25 00:11:45 kernel: [  706.038336] Get brightness: 100
Jan 25 00:11:45 kernel: [  706.038428] Brightness register values: 00 64
Jan 25 00:11:45 kernel: [  706.038429] Get brightness: 100
Jan 25 00:11:45 kernel: [  706.078390] Brightness register values: 00 50
Jan 25 00:11:45 kernel: [  706.078399] Get brightness: 80
Jan 25 00:11:45 kernel: [  706.078509] Brightness register values: 00 50
Jan 25 00:11:45 kernel: [  706.078511] Get brightness: 80
Jan 25 00:11:45 kernel: [  706.118448] Brightness register values: 00 3c
Jan 25 00:11:45 kernel: [  706.118458] Get brightness: 60
Jan 25 00:11:45 kernel: [  706.118552] Brightness register values: 00 3c
Jan 25 00:11:45 kernel: [  706.118553] Get brightness: 60
Jan 25 00:11:45 kernel: [  706.158425] Brightness register values: 00 28
Jan 25 00:11:45 kernel: [  706.158435] Get brightness: 40
Jan 25 00:11:45 kernel: [  706.158547] Brightness register values: 00 28
Jan 25 00:11:45 kernel: [  706.158549] Get brightness: 40
Jan 25 00:11:45 kernel: [  706.198389] Brightness register values: 00 14
Jan 25 00:11:45 kernel: [  706.198401] Get brightness: 20
Jan 25 00:11:45 kernel: [  706.198490] Brightness register values: 00 14
Jan 25 00:11:45 kernel: [  706.198492] Get brightness: 20
Jan 25 00:14:57 kernel: [  897.692134] mmc5603 5-0030: [MMC5603] Mmc5603 data is not ready,status=16
Jan 25 00:15:36 kernel: [  937.445740] Get switch state: 0
Jan 25 00:15:36 kernel: [  937.445824] Get switch state: 0
Jan 25 00:15:37 kernel: [  937.467007] Brightness register values: 00 00
Jan 25 00:15:37 kernel: [  937.467016] Get brightness: 0
Jan 25 00:15:37 kernel: [  937.467109] Brightness register values: 00 00
Jan 25 00:15:37 kernel: [  937.467110] Get brightness: 0
Jan 25 00:15:37 kernel: [  937.498346] Brightness register values: 00 00
Jan 25 00:15:37 kernel: [  937.498353] Get brightness: 0
Jan 25 00:15:37 kernel: [  937.498448] Brightness register values: 00 00
Jan 25 00:15:37 kernel: [  937.498450] Get brightness: 0
Jan 25 00:15:37 kernel: [  937.538331] Brightness register values: 00 32
Jan 25 00:15:37 kernel: [  937.538342] Get brightness: 50
Jan 25 00:15:37 kernel: [  937.538438] Brightness register values: 00 32
Jan 25 00:15:37 kernel: [  937.538440] Get brightness: 50
Jan 25 00:15:37 kernel: [  937.578390] Brightness register values: 00 64
Jan 25 00:15:37 kernel: [  937.578400] Get brightness: 100
Jan 25 00:15:37 kernel: [  937.578498] Brightness register values: 00 64
Jan 25 00:15:37 kernel: [  937.578500] Get brightness: 100
Jan 25 00:15:37 kernel: [  937.618477] Brightness register values: 00 96
Jan 25 00:15:37 kernel: [  937.618488] Get brightness: 150
Jan 25 00:15:37 kernel: [  937.618580] Brightness register values: 00 96
Jan 25 00:15:37 kernel: [  937.618582] Get brightness: 150
Jan 25 00:15:37 kernel: [  937.658356] Brightness register values: 00 c8
Jan 25 00:15:37 kernel: [  937.658368] Get brightness: 200
Jan 25 00:15:37 kernel: [  937.658455] Brightness register values: 00 c8
Jan 25 00:15:37 kernel: [  937.658456] Get brightness: 200
Jan 25 00:15:37 kernel: [  937.698453] Brightness register values: 00 fa
Jan 25 00:15:37 kernel: [  937.698463] Get brightness: 250
Jan 25 00:15:37 kernel: [  937.698557] Brightness register values: 00 fa
Jan 25 00:15:37 kernel: [  937.698559] Get brightness: 250
Jan 25 00:15:37 kernel: [  937.738344] Brightness register values: 01 2c
Jan 25 00:15:37 kernel: [  937.738354] Get brightness: 300
Jan 25 00:15:37 kernel: [  937.738447] Brightness register values: 01 2c
Jan 25 00:15:37 kernel: [  937.738448] Get brightness: 300
Jan 25 00:15:37 kernel: [  937.778378] Brightness register values: 01 5e
Jan 25 00:15:37 kernel: [  937.778387] Get brightness: 350
Jan 25 00:15:37 kernel: [  937.778480] Brightness register values: 01 5e
Jan 25 00:15:37 kernel: [  937.778482] Get brightness: 350
Jan 25 00:15:37 kernel: [  937.818391] Brightness register values: 01 90
Jan 25 00:15:37 kernel: [  937.818401] Get brightness: 400
Jan 25 00:15:37 kernel: [  937.818500] Brightness register values: 01 90
Jan 25 00:15:37 kernel: [  937.818502] Get brightness: 400
Jan 25 00:15:37 kernel: [  937.858399] Brightness register values: 01 c2
Jan 25 00:15:37 kernel: [  937.858409] Get brightness: 450
Jan 25 00:15:37 kernel: [  937.858503] Brightness register values: 01 c2
Jan 25 00:15:37 kernel: [  937.858505] Get brightness: 450
Jan 25 00:15:38 kernel: [  938.840398] Get switch state: 1
Jan 25 00:15:38 kernel: [  938.840486] Get switch state: 1
Jan 25 00:16:08 kernel: [  969.398562] Get switch state: 1
Jan 25 00:16:08 kernel: [  969.398658] Get switch state: 1
Jan 25 00:16:08 kernel: [  969.400811] Get switch state: 1
Jan 25 00:16:08 kernel: [  969.400892] Get switch state: 1
Jan 25 00:16:08 kernel: [  969.401357] Brightness register values: 01 f4
Jan 25 00:16:08 kernel: [  969.401364] Get brightness: 500
Jan 25 00:16:08 kernel: [  969.401438] Brightness register values: 01 f4
Jan 25 00:16:08 kernel: [  969.401439] Get brightness: 500
Jan 25 00:16:10 kernel: [  970.604569] get_ts_config index = 0 
Jan 25 00:16:10 kernel: [  970.604579] timestamp_record_open, clear kfifo and enable irq, sensor index = 0 
Jan 25 00:16:10 kernel: [  970.605467] dev_0 SUB_CLEAR success
Jan 25 00:16:10 kernel: [  970.605928] dev_0 free IRQ success
Jan 25 00:16:10 kernel: [  970.606865] dev_1 SUB_CLEAR success
Jan 25 00:16:10 kernel: [  970.608252] dev_1 free IRQ success
Jan 25 00:16:10 kernel: [  970.626495] mmz_userdev:get_mmbinfo_safe: 
Jan 25 00:16:10 kernel: [  970.626503] mmb(0x3CC6F000) not found!
Jan 25 00:16:10 kernel: [  970.626955] mmz_userdev:get_mmbinfo_safe: 
Jan 25 00:16:10 kernel: [  970.626960] mmb(0x3CC70000) not found!
Jan 25 00:16:10 kernel: [  970.627060] mmz_userdev:get_mmbinfo_safe: 
Jan 25 00:16:10 kernel: [  970.627062] mmb(0x3CC71000) not found!
Jan 25 00:16:10 kernel: [  970.627506] mmz_userdev:get_mmbinfo_safe: 
Jan 25 00:16:10 kernel: [  970.627509] mmb(0x3CC72000) not found!
Jan 25 00:16:10 kernel: [  970.627840] mmz_userdev:get_mmbinfo_safe: 
Jan 25 00:16:10 kernel: [  970.627843] mmb(0x3CC73000) not found!
Jan 25 00:16:10 kernel: [  970.627900] mmz_userdev:get_mmbinfo_safe: 
Jan 25 00:16:10 kernel: [  970.627901] mmb(0x3CC74000) not found!
Jan 25 00:16:10 kernel: [  970.627947] mmz_userdev:get_mmbinfo_safe: 
Jan 25 00:16:10 kernel: [  970.627949] mmb(0x3CC75000) not found!
Jan 25 00:16:10 kernel: [  970.627996] mmz_userdev:get_mmbinfo_safe: 
Jan 25 00:16:10 kernel: [  970.627998] mmb(0x3CC76000) not found!
Jan 25 00:16:10 kernel: [  970.631458] OLED:ecx348_left initialized successfully with config_index: 3
Jan 25 00:16:10 kernel: [  970.631465] OLED initialized successfully with initial_code_idx: 3
Jan 25 00:16:10 kernel: [  970.632882] OLED:ecx348_right initialized successfully with config_index: 3
Jan 25 00:16:10 kernel: [  970.632889] OLED initialized successfully with initial_code_idx: 3
Jan 25 00:16:10 kernel: [  970.633030] Set orbit_h: 0
Jan 25 00:16:10 kernel: [  970.633109] Set orbit_h: 0
Jan 25 00:16:10 kernel: [  970.633186] Set orbit_v: 0
Jan 25 00:16:10 kernel: [  970.633263] Set orbit_v: 0
Jan 25 00:16:10 kernel: [  970.836724] dev_0 request  IRQ success
Jan 25 00:16:10 kernel: [  970.840048] dev_1 request  IRQ success
Jan 25 00:16:10 kernel: [  970.841706] Enabling LVDS lowest power...
Jan 25 00:16:10 kernel: [  970.841719] Write Value: 0x80 to Address: 0xffffff8008edd018
Jan 25 00:16:10 kernel: [  970.841721] Verification successful at Address: 0xffffff8008edd018. Value: 0x80
Jan 25 00:16:10 kernel: [  970.841723] Write Value: 0x1a to Address: 0xffffff8008edd390
Jan 25 00:16:10 kernel: [  970.841725] Verification successful at Address: 0xffffff8008edd390. Value: 0x1a
Jan 25 00:16:10 kernel: [  970.841726] Write Value: 0x1 to Address: 0xffffff8008edd0c0
Jan 25 00:16:10 kernel: [  970.841728] Verification successful at Address: 0xffffff8008edd0c0. Value: 0x1
Jan 25 00:16:10 kernel: [  970.841730] Write Value: 0xc0 to Address: 0xffffff8008edd39c
Jan 25 00:16:10 kernel: [  970.841731] Verification successful at Address: 0xffffff8008edd39c. Value: 0xc0
Jan 25 00:16:10 kernel: [  970.841733] Write Value: 0xad to Address: 0xffffff8008edd3a0
Jan 25 00:16:10 kernel: [  970.841735] Verification successful at Address: 0xffffff8008edd3a0. Value: 0xad
Jan 25 00:16:10 kernel: [  970.841736] Write Value: 0x80 to Address: 0xffffff8008f03418
Jan 25 00:16:10 kernel: [  970.841738] Verification successful at Address: 0xffffff8008f03418. Value: 0x80
Jan 25 00:16:10 kernel: [  970.841740] Write Value: 0x1a to Address: 0xffffff8008f03790
Jan 25 00:16:10 kernel: [  970.841742] Verification successful at Address: 0xffffff8008f03790. Value: 0x1a
Jan 25 00:16:10 kernel: [  970.841744] Write Value: 0x1 to Address: 0xffffff8008f034c0
Jan 25 00:16:10 kernel: [  970.841746] Verification successful at Address: 0xffffff8008f034c0. Value: 0x1
Jan 25 00:16:10 kernel: [  970.841747] Write Value: 0xc0 to Address: 0xffffff8008f0379c
Jan 25 00:16:10 kernel: [  970.841749] Verification successful at Address: 0xffffff8008f0379c. Value: 0xc0
Jan 25 00:16:10 kernel: [  970.841750] Write Value: 0xad to Address: 0xffffff8008f037a0
Jan 25 00:16:10 kernel: [  970.841752] Verification successful at Address: 0xffffff8008f037a0. Value: 0xad
Jan 25 00:16:10 kernel: [  970.841754] Write Value: 0x80 to Address: 0xffffff8008f05018
Jan 25 00:16:10 kernel: [  970.841755] Verification successful at Address: 0xffffff8008f05018. Value: 0x80
Jan 25 00:16:10 kernel: [  970.841757] Write Value: 0x1a to Address: 0xffffff8008f05390
Jan 25 00:16:10 kernel: [  970.841759] Verification successful at Address: 0xffffff8008f05390. Value: 0x1a
Jan 25 00:16:10 kernel: [  970.841760] Write Value: 0x1 to Address: 0xffffff8008f050c0
Jan 25 00:16:10 kernel: [  970.841762] Verification successful at Address: 0xffffff8008f050c0. Value: 0x1
Jan 25 00:16:10 kernel: [  970.841763] Write Value: 0xc0 to Address: 0xffffff8008f0539c
Jan 25 00:16:10 kernel: [  970.841765] Verification successful at Address: 0xffffff8008f0539c. Value: 0xc0
Jan 25 00:16:10 kernel: [  970.841766] Write Value: 0xad to Address: 0xffffff8008f053a0
Jan 25 00:16:10 kernel: [  970.841768] Verification successful at Address: 0xffffff8008f053a0. Value: 0xad
Jan 25 00:16:10 kernel: [  970.841770] Write Value: 0x80 to Address: 0xffffff8008f07418
Jan 25 00:16:10 kernel: [  970.841771] Verification successful at Address: 0xffffff8008f07418. Value: 0x80
Jan 25 00:16:10 kernel: [  970.841773] Write Value: 0x1a to Address: 0xffffff8008f07790
Jan 25 00:16:10 kernel: [  970.841775] Verification successful at Address: 0xffffff8008f07790. Value: 0x1a
Jan 25 00:16:10 kernel: [  970.841776] Write Value: 0x1 to Address: 0xffffff8008f074c0
Jan 25 00:16:10 kernel: [  970.841778] Verification successful at Address: 0xffffff8008f074c0. Value: 0x1
Jan 25 00:16:10 kernel: [  970.841779] Write Value: 0xc0 to Address: 0xffffff8008f0779c
Jan 25 00:16:10 kernel: [  970.841781] Verification successful at Address: 0xffffff8008f0779c. Value: 0xc0
Jan 25 00:16:10 kernel: [  970.841782] Write Value: 0xad to Address: 0xffffff8008f077a0
Jan 25 00:16:10 kernel: [  970.841784] Verification successful at Address: 0xffffff8008f077a0. Value: 0xad
Jan 25 00:16:10 kernel: [  970.843007] Get switch state: 0
Jan 25 00:16:10 kernel: [  970.843087] Get switch state: 0
Jan 25 00:16:10 kernel: [  970.864289] Brightness register values: 00 00
Jan 25 00:16:10 kernel: [  970.864299] Get brightness: 0
Jan 25 00:16:10 kernel: [  970.864374] Brightness register values: 00 00
Jan 25 00:16:10 kernel: [  970.864375] Get brightness: 0
Jan 25 00:16:10 kernel: [  970.864827] Get switch state: 1
Jan 25 00:16:10 kernel: [  970.864916] Get switch state: 1
Jan 25 00:16:10 kernel: [  970.866133] Set white coordinate x:-31, y:-31
Jan 25 00:16:10 kernel: [  970.866770] Set white coordinate x:-31, y:-31
Jan 25 00:16:10 kernel: [  970.915836] Brightness register values: 00 00
Jan 25 00:16:10 kernel: [  970.915843] Get brightness: 0
Jan 25 00:16:10 kernel: [  970.915920] Brightness register values: 00 00
Jan 25 00:16:10 kernel: [  970.915921] Get brightness: 0
Jan 25 00:16:10 kernel: [  970.937016] Brightness register values: 00 32
Jan 25 00:16:10 kernel: [  970.937038] Get brightness: 50
Jan 25 00:16:10 kernel: [  970.937125] Brightness register values: 00 32
Jan 25 00:16:10 kernel: [  970.937134] Get brightness: 50
Jan 25 00:16:10 kernel: [  970.958437] Brightness register values: 00 64
Jan 25 00:16:10 kernel: [  970.958447] Get brightness: 100
Jan 25 00:16:10 kernel: [  970.958534] Brightness register values: 00 64
Jan 25 00:16:10 kernel: [  970.958535] Get brightness: 100
Jan 25 00:16:10 kernel: [  970.979836] Brightness register values: 00 96
Jan 25 00:16:10 kernel: [  970.979850] Get brightness: 150
Jan 25 00:16:10 kernel: [  970.979949] Brightness register values: 00 96
Jan 25 00:16:10 kernel: [  970.979951] Get brightness: 150
Jan 25 00:16:10 kernel: [  971.001039] Brightness register values: 00 c8
Jan 25 00:16:10 kernel: [  971.001051] Get brightness: 200
Jan 25 00:16:10 kernel: [  971.001145] Brightness register values: 00 c8
Jan 25 00:16:10 kernel: [  971.001146] Get brightness: 200
Jan 25 00:16:10 kernel: [  971.022222] Brightness register values: 00 fa
Jan 25 00:16:10 kernel: [  971.022234] Get brightness: 250
Jan 25 00:16:10 kernel: [  971.022319] Brightness register values: 00 fa
Jan 25 00:16:10 kernel: [  971.022320] Get brightness: 250
Jan 25 00:16:10 kernel: [  971.043470] Brightness register values: 01 2c
Jan 25 00:16:10 kernel: [  971.043480] Get brightness: 300
Jan 25 00:16:10 kernel: [  971.043561] Brightness register values: 01 2c
Jan 25 00:16:10 kernel: [  971.043563] Get brightness: 300
Jan 25 00:16:10 kernel: [  971.064831] Brightness register values: 01 5e
Jan 25 00:16:10 kernel: [  971.064841] Get brightness: 350
Jan 25 00:16:10 kernel: [  971.064943] Brightness register values: 01 5e
Jan 25 00:16:10 kernel: [  971.064946] Get brightness: 350
Jan 25 00:16:10 kernel: [  971.086182] Brightness register values: 01 90
Jan 25 00:16:10 kernel: [  971.086191] Get brightness: 400
Jan 25 00:16:10 kernel: [  971.086277] Brightness register values: 01 90
Jan 25 00:16:10 kernel: [  971.086279] Get brightness: 400
Jan 25 00:16:10 kernel: [  971.107224] Brightness register values: 01 c2
Jan 25 00:16:10 kernel: [  971.107236] Get brightness: 450
Jan 25 00:16:10 kernel: [  971.107366] Brightness register values: 01 c2
Jan 25 00:16:10 kernel: [  971.107368] Get brightness: 450
Jan 25 00:19:44 kernel: [ 1185.186290] Get switch state: 1
Jan 25 00:19:44 kernel: [ 1185.186379] Get switch state: 1
Jan 25 00:19:44 kernel: [ 1185.186733] Brightness register values: 01 f4
Jan 25 00:19:44 kernel: [ 1185.186740] Get brightness: 500
Jan 25 00:19:44 kernel: [ 1185.186823] Brightness register values: 01 f4
Jan 25 00:19:44 kernel: [ 1185.186825] Get brightness: 500
Jan 25 00:19:44 kernel: [ 1185.218633] Brightness register values: 01 f4
Jan 25 00:19:44 kernel: [ 1185.218644] Get brightness: 500
Jan 25 00:19:44 kernel: [ 1185.218743] Brightness register values: 01 f4
Jan 25 00:19:44 kernel: [ 1185.218745] Get brightness: 500
Jan 25 00:19:44 kernel: [ 1185.258364] Brightness register values: 01 e0
Jan 25 00:19:44 kernel: [ 1185.258375] Get brightness: 480
Jan 25 00:19:44 kernel: [ 1185.258468] Brightness register values: 01 e0
Jan 25 00:19:44 kernel: [ 1185.258469] Get brightness: 480
Jan 25 00:19:44 kernel: [ 1185.298388] Brightness register values: 01 cc
Jan 25 00:19:44 kernel: [ 1185.298398] Get brightness: 460
Jan 25 00:19:44 kernel: [ 1185.298485] Brightness register values: 01 cc
Jan 25 00:19:44 kernel: [ 1185.298487] Get brightness: 460
Jan 25 00:19:44 kernel: [ 1185.338353] Brightness register values: 01 b8
Jan 25 00:19:44 kernel: [ 1185.338364] Get brightness: 440
Jan 25 00:19:44 kernel: [ 1185.338457] Brightness register values: 01 b8
Jan 25 00:19:44 kernel: [ 1185.338459] Get brightness: 440
Jan 25 00:19:44 kernel: [ 1185.378426] Brightness register values: 01 a4
Jan 25 00:19:44 kernel: [ 1185.378438] Get brightness: 420
Jan 25 00:19:44 kernel: [ 1185.378523] Brightness register values: 01 a4
Jan 25 00:19:44 kernel: [ 1185.378525] Get brightness: 420
Jan 25 00:19:44 kernel: [ 1185.418531] Brightness register values: 01 90
Jan 25 00:19:44 kernel: [ 1185.418542] Get brightness: 400
Jan 25 00:19:44 kernel: [ 1185.418686] Brightness register values: 01 90
Jan 25 00:19:44 kernel: [ 1185.418689] Get brightness: 400
Jan 25 00:19:45 kernel: [ 1185.458371] Brightness register values: 01 7c
Jan 25 00:19:45 kernel: [ 1185.458379] Get brightness: 380
Jan 25 00:19:45 kernel: [ 1185.458473] Brightness register values: 01 7c
Jan 25 00:19:45 kernel: [ 1185.458475] Get brightness: 380
Jan 25 00:19:45 kernel: [ 1185.498375] Brightness register values: 01 68
Jan 25 00:19:45 kernel: [ 1185.498394] Get brightness: 360
Jan 25 00:19:45 kernel: [ 1185.498492] Brightness register values: 01 68
Jan 25 00:19:45 kernel: [ 1185.498494] Get brightness: 360
Jan 25 00:19:45 kernel: [ 1185.538367] Brightness register values: 01 54
Jan 25 00:19:45 kernel: [ 1185.538378] Get brightness: 340
Jan 25 00:19:45 kernel: [ 1185.538465] Brightness register values: 01 54
Jan 25 00:19:45 kernel: [ 1185.538466] Get brightness: 340
Jan 25 00:19:45 kernel: [ 1185.578408] Brightness register values: 01 40
Jan 25 00:19:45 kernel: [ 1185.578421] Get brightness: 320
Jan 25 00:19:45 kernel: [ 1185.578507] Brightness register values: 01 40
Jan 25 00:19:45 kernel: [ 1185.578509] Get brightness: 320
Jan 25 00:19:45 kernel: [ 1185.618773] Brightness register values: 01 2c
Jan 25 00:19:45 kernel: [ 1185.618785] Get brightness: 300
Jan 25 00:19:45 kernel: [ 1185.618887] Brightness register values: 01 2c
Jan 25 00:19:45 kernel: [ 1185.618889] Get brightness: 300
Jan 25 00:19:45 kernel: [ 1185.658376] Brightness register values: 01 18
Jan 25 00:19:45 kernel: [ 1185.658386] Get brightness: 280
Jan 25 00:19:45 kernel: [ 1185.658472] Brightness register values: 01 18
Jan 25 00:19:45 kernel: [ 1185.658475] Get brightness: 280
Jan 25 00:19:45 kernel: [ 1185.698480] Brightness register values: 01 04
Jan 25 00:19:45 kernel: [ 1185.698490] Get brightness: 260
Jan 25 00:19:45 kernel: [ 1185.698584] Brightness register values: 01 04
Jan 25 00:19:45 kernel: [ 1185.698586] Get brightness: 260
Jan 25 00:19:45 kernel: [ 1185.738311] Brightness register values: 00 f0
Jan 25 00:19:45 kernel: [ 1185.738323] Get brightness: 240
Jan 25 00:19:45 kernel: [ 1185.738416] Brightness register values: 00 f0
Jan 25 00:19:45 kernel: [ 1185.738418] Get brightness: 240
Jan 25 00:19:45 kernel: [ 1185.778330] Brightness register values: 00 dc
Jan 25 00:19:45 kernel: [ 1185.778340] Get brightness: 220
Jan 25 00:19:45 kernel: [ 1185.778428] Brightness register values: 00 dc
Jan 25 00:19:45 kernel: [ 1185.778430] Get brightness: 220
Jan 25 00:19:45 kernel: [ 1185.818905] Brightness register values: 00 c8
Jan 25 00:19:45 kernel: [ 1185.818916] Get brightness: 200
Jan 25 00:19:45 kernel: [ 1185.819045] Brightness register values: 00 c8
Jan 25 00:19:45 kernel: [ 1185.819048] Get brightness: 200
Jan 25 00:19:45 kernel: [ 1185.858416] Brightness register values: 00 b4
Jan 25 00:19:45 kernel: [ 1185.858427] Get brightness: 180
Jan 25 00:19:45 kernel: [ 1185.858516] Brightness register values: 00 b4
Jan 25 00:19:45 kernel: [ 1185.858518] Get brightness: 180
Jan 25 00:19:45 kernel: [ 1185.898405] Brightness register values: 00 a0
Jan 25 00:19:45 kernel: [ 1185.898415] Get brightness: 160
Jan 25 00:19:45 kernel: [ 1185.898508] Brightness register values: 00 a0
Jan 25 00:19:45 kernel: [ 1185.898510] Get brightness: 160
Jan 25 00:19:45 kernel: [ 1185.938331] Brightness register values: 00 8c
Jan 25 00:19:45 kernel: [ 1185.938343] Get brightness: 140
Jan 25 00:19:45 kernel: [ 1185.938429] Brightness register values: 00 8c
Jan 25 00:19:45 kernel: [ 1185.938430] Get brightness: 140
Jan 25 00:19:45 kernel: [ 1185.978336] Brightness register values: 00 78
Jan 25 00:19:45 kernel: [ 1185.978346] Get brightness: 120
Jan 25 00:19:45 kernel: [ 1185.978433] Brightness register values: 00 78
Jan 25 00:19:45 kernel: [ 1185.978435] Get brightness: 120
Jan 25 00:19:45 kernel: [ 1186.018589] Brightness register values: 00 64
Jan 25 00:19:45 kernel: [ 1186.018608] Get brightness: 100
Jan 25 00:19:45 kernel: [ 1186.018726] Brightness register values: 00 64
Jan 25 00:19:45 kernel: [ 1186.018729] Get brightness: 100
Jan 25 00:19:45 kernel: [ 1186.058328] Brightness register values: 00 50
Jan 25 00:19:45 kernel: [ 1186.058338] Get brightness: 80
Jan 25 00:19:45 kernel: [ 1186.058430] Brightness register values: 00 50
Jan 25 00:19:45 kernel: [ 1186.058431] Get brightness: 80
Jan 25 00:19:45 kernel: [ 1186.098413] Brightness register values: 00 3c
Jan 25 00:19:45 kernel: [ 1186.098426] Get brightness: 60
Jan 25 00:19:45 kernel: [ 1186.098522] Brightness register values: 00 3c
Jan 25 00:19:45 kernel: [ 1186.098523] Get brightness: 60
Jan 25 00:19:45 kernel: [ 1186.138426] Brightness register values: 00 28
Jan 25 00:19:45 kernel: [ 1186.138436] Get brightness: 40
Jan 25 00:19:45 kernel: [ 1186.138533] Brightness register values: 00 28
Jan 25 00:19:45 kernel: [ 1186.138534] Get brightness: 40
Jan 25 00:19:45 kernel: [ 1186.178454] Brightness register values: 00 14
Jan 25 00:19:45 kernel: [ 1186.178463] Get brightness: 20
Jan 25 00:19:45 kernel: [ 1186.178550] Brightness register values: 00 14
Jan 25 00:19:45 kernel: [ 1186.178552] Get brightness: 20
Jan 25 00:27:03 kernel: [ 1623.992298] vo_sub_1_from_d[9434]: unhandled level 0 translation fault (11) at 0x43800000778, esr 0x92000004
Jan 25 00:27:03 kernel: [ 1623.992308] pgd = ffffffc03039b000
Jan 25 00:27:03 kernel: [ 1623.992311] [43800000778] *pgd=0000000000000000
Jan 25 00:27:03 kernel: [ 1623.992313] , *pud=0000000000000000
Jan 25 00:27:03 kernel: [ 1623.992314] 
Jan 25 00:27:03 kernel: [ 1623.992316] 
Jan 25 00:27:03 kernel: [ 1623.992325] CPU: 2 PID: 9434 Comm: vo_sub_1_from_d Tainted: G           O    4.9.38 #3
Jan 25 00:27:03 kernel: [ 1623.992328] Hardware name: Artosyn, Kuiper Development Board (DT)
Jan 25 00:27:03 kernel: [ 1623.992331] task: ffffffc02a413700 task.stack: ffffffc02a5d8000
Jan 25 00:27:03 kernel: [ 1623.992336] PC is at 0x7f7f13f6c0
Jan 25 00:27:03 kernel: [ 1623.992338] LR is at 0x7f7d8f49f8
Jan 25 00:27:03 kernel: [ 1623.992340] pc : [<0000007f7f13f6c0>] lr : [<0000007f7d8f49f8>] pstate: 20000000
Jan 25 00:27:03 kernel: [ 1623.992342] sp : 0000007f5405e5e0
Jan 25 00:27:03 kernel: [ 1623.992344] x29: 0000007f5405e5e0 x28: 0000000000039e00 
Jan 25 00:27:03 kernel: [ 1623.992348] x27: 0000000000000000 x26: 0000007f7dd25ed0 
Jan 25 00:27:03 kernel: [ 1623.992352] x25: 0000000000000013 x24: 0000007f540608b0 
Jan 25 00:27:03 kernel: [ 1623.992356] x23: 0000017a1cb347b0 x22: 0000000000000000 
Jan 25 00:27:03 kernel: [ 1623.992359] x21: 0000017a1cf2fba2 x20: 0000017a1cc7cfde 
Jan 25 00:27:03 kernel: [ 1623.992363] x19: 0000007f7dd259b0 x18: 0000000000000000 
Jan 25 00:27:03 kernel: [ 1623.992367] x17: 0000007f7f13f6a0 x16: 0000007f7f47e698 
Jan 25 00:27:03 kernel: [ 1623.992371] x15: 000080015e565b7d x14: 003a1c59cc5d4e00 
Jan 25 00:27:03 kernel: [ 1623.992375] x13: 0000000000000657 x12: 0000000000000018 
Jan 25 00:27:03 kernel: [ 1623.992378] x11: 000000003a9c5b2a x10: 0000000000000657 
Jan 25 00:27:03 kernel: [ 1623.992382] x9 : 003b9aca00000000 x8 : 00ffffffffffffff 
Jan 25 00:27:03 kernel: [ 1623.992386] x7 : 000000000004f4a8 x6 : 0000007f7f517000 
Jan 25 00:27:03 kernel: [ 1623.992390] x5 : 00000000000002c0 x4 : 0000000000000000 
Jan 25 00:27:03 kernel: [ 1623.992394] x3 : 0000000000000013 x2 : 0000000000000000 
Jan 25 00:27:03 kernel: [ 1623.992397] x1 : 0000043800000780 x0 : 0000043800000780 
Jan 25 00:27:03 kernel: [ 1623.992400] 
Jan 25 00:27:03 kernel: [ 1623.992791] wait_event_interruptible ret = -512 
Jan 25 00:27:03 kernel: [ 1623.993066] iqs323 3-0044: iqs323_dev_read: wait_event_interruptible has a process dead.
