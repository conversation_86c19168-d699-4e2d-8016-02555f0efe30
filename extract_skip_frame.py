#!/usr/bin/env python3
"""
脚本用于从pilot.log文件中提取skip_frame数据并保存为CSV格式
可以用Excel或其他工具打开CSV文件进行绘图
"""

import re
import csv
from datetime import datetime

def parse_pilot_log(log_file_path):
    """
    解析pilot.log文件，提取时间戳和skip_frame值
    
    Args:
        log_file_path (str): pilot.log文件路径
        
    Returns:
        list: 包含(timestamp, skip_frame)元组的列表
    """
    data = []
    
    # 正则表达式匹配包含skip_frame的行
    # 示例行: Jan 25 00:00:01 XREAL[529]: [2027-01-25 00:00:01.515] [829] [DEBUG] [Flinger] frame_not_shown:0 skip_frame:0 addr_mismatch:0 pose_error:0
    pattern = r'(\w{3} \d{2} \d{2}:\d{2}:\d{2}).*?\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\].*?skip_frame:(\d+)'
    
    try:
        with open(log_file_path, 'r', encoding='utf-8') as file:
            for line_num, line in enumerate(file, 1):
                match = re.search(pattern, line)
                if match:
                    # 提取时间戳和skip_frame值
                    system_time = match.group(1)
                    app_timestamp = match.group(2)
                    skip_frame = int(match.group(3))
                    
                    # 使用应用程序时间戳（更精确）
                    try:
                        dt = datetime.strptime(app_timestamp, '%Y-%m-%d %H:%M:%S.%f')
                        data.append((dt, skip_frame))
                    except ValueError:
                        print(f"警告: 第{line_num}行时间戳格式错误: {app_timestamp}")
                        continue
                        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {log_file_path}")
        return []
    except Exception as e:
        print(f"错误: 读取文件时发生异常: {e}")
        return []
    
    print(f"成功解析 {len(data)} 条skip_frame记录")
    return data

def save_to_csv(data, output_file):
    """
    将数据保存为CSV文件
    
    Args:
        data (list): 包含(timestamp, skip_frame)元组的列表
        output_file (str): 输出CSV文件路径
    """
    try:
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            # 写入表头
            writer.writerow(['时间戳', 'Skip Frame'])
            
            # 写入数据
            for timestamp, skip_frame in data:
                writer.writerow([timestamp.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3], skip_frame])
        
        print(f"数据已保存到CSV文件: {output_file}")
    except Exception as e:
        print(f"保存CSV文件时发生错误: {e}")

def print_statistics(data):
    """
    打印统计信息
    
    Args:
        data (list): 包含(timestamp, skip_frame)元组的列表
    """
    if not data:
        print("没有数据可以分析")
        return
    
    skip_frame_values = [item[1] for item in data]
    timestamps = [item[0] for item in data]
    
    print(f"\n=== 统计信息 ===")
    print(f"数据点总数: {len(data)}")
    print(f"时间范围: {timestamps[0]} 到 {timestamps[-1]}")
    print(f"skip_frame最大值: {max(skip_frame_values)}")
    print(f"skip_frame最小值: {min(skip_frame_values)}")
    print(f"skip_frame平均值: {sum(skip_frame_values)/len(skip_frame_values):.2f}")
    
    # 计算skip_frame变化趋势
    if len(skip_frame_values) > 1:
        increases = sum(1 for i in range(1, len(skip_frame_values)) 
                      if skip_frame_values[i] > skip_frame_values[i-1])
        decreases = sum(1 for i in range(1, len(skip_frame_values)) 
                      if skip_frame_values[i] < skip_frame_values[i-1])
        unchanged = len(skip_frame_values) - 1 - increases - decreases
        
        print(f"skip_frame增长次数: {increases}")
        print(f"skip_frame减少次数: {decreases}")
        print(f"skip_frame不变次数: {unchanged}")
    
    # 显示前10个和后10个数据点
    print(f"\n=== 前10个数据点 ===")
    for i, (timestamp, skip_frame) in enumerate(data[:10]):
        print(f"{i+1:2d}. {timestamp.strftime('%H:%M:%S.%f')[:-3]} -> {skip_frame}")
    
    if len(data) > 10:
        print(f"\n=== 后10个数据点 ===")
        for i, (timestamp, skip_frame) in enumerate(data[-10:], len(data)-9):
            print(f"{i:2d}. {timestamp.strftime('%H:%M:%S.%f')[:-3]} -> {skip_frame}")

def create_simple_plot_data(data, output_file):
    """
    创建简单的文本格式图表数据
    
    Args:
        data (list): 包含(timestamp, skip_frame)元组的列表
        output_file (str): 输出文件路径
    """
    if not data:
        return
    
    skip_frame_values = [item[1] for item in data]
    max_value = max(skip_frame_values)
    
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("Skip Frame 随时间变化的简单文本图表\n")
            f.write("=" * 50 + "\n\n")
            
            # 每隔一定间隔显示一个数据点
            step = max(1, len(data) // 50)  # 最多显示50个点
            
            for i in range(0, len(data), step):
                timestamp, skip_frame = data[i]
                time_str = timestamp.strftime('%H:%M:%S')
                
                # 创建简单的条形图
                bar_length = int((skip_frame / max_value) * 40) if max_value > 0 else 0
                bar = '█' * bar_length
                
                f.write(f"{time_str} [{skip_frame:3d}] {bar}\n")
        
        print(f"简单文本图表已保存到: {output_file}")
    except Exception as e:
        print(f"保存文本图表时发生错误: {e}")

def main():
    """主函数"""
    log_file = 'pilot.log'
    csv_output = 'skip_frame_data.csv'
    text_plot_output = 'skip_frame_plot.txt'
    
    print("开始解析pilot.log文件...")
    data = parse_pilot_log(log_file)
    
    if data:
        # 保存为CSV文件
        save_to_csv(data, csv_output)
        
        # 创建简单的文本图表
        create_simple_plot_data(data, text_plot_output)
        
        # 打印统计信息
        print_statistics(data)
        
        print(f"\n=== 输出文件 ===")
        print(f"1. CSV数据文件: {csv_output}")
        print(f"   (可以用Excel打开并创建图表)")
        print(f"2. 文本图表文件: {text_plot_output}")
        print(f"   (简单的文本格式图表)")
        
    else:
        print("未找到有效的skip_frame数据")

if __name__ == "__main__":
    main()
