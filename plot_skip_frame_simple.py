#!/usr/bin/env python3
"""
从pilot.log中提取skip_frame数据并绘制折线图
"""

import re
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime

# 设置matplotlib支持中文
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans', 'Liberation Sans']
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

def extract_and_plot():
    """提取数据并绘图"""
    timestamps = []
    skip_frame_values = []
    
    # 正则表达式匹配skip_frame行
    pattern = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\].*?skip_frame:(\d+)'
    
    # 尝试不同编码读取文件
    encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            print(f"尝试编码: {encoding}")
            with open('pilot.log', 'r', encoding=encoding) as file:
                for line in file:
                    if 'skip_frame:' in line:
                        match = re.search(pattern, line)
                        if match:
                            timestamp_str = match.group(1)
                            skip_frame = int(match.group(2))
                            
                            try:
                                dt = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S.%f')
                                timestamps.append(dt)
                                skip_frame_values.append(skip_frame)
                            except ValueError:
                                continue
            
            print(f"成功提取 {len(timestamps)} 个数据点")
            break
            
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"读取文件出错: {e}")
            return
    
    if not timestamps:
        print("未找到skip_frame数据")
        return
    
    # 绘制图表
    plt.figure(figsize=(15, 8))
    
    # 绘制折线图
    plt.plot(timestamps, skip_frame_values, 'b-', linewidth=1.5, alpha=0.8, label='skip_frame')
    
    # 添加散点
    plt.scatter(timestamps, skip_frame_values, c='red', s=15, alpha=0.6, zorder=5)
    
    # 设置标题和标签
    plt.title('Skip Frame Over Time Trend', fontsize=16, fontweight='bold')
    plt.xlabel('Time', fontsize=12)
    plt.ylabel('Skip Frame Count', fontsize=12)
    
    # 设置网格
    plt.grid(True, alpha=0.3, linestyle='--')
    
    # 格式化时间轴
    ax = plt.gca()
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M:%S'))
    ax.xaxis.set_major_locator(mdates.MinuteLocator(interval=2))
    plt.xticks(rotation=45)
    
    # 设置y轴从0开始
    plt.ylim(bottom=0)
    
    # 添加统计信息
    max_skip = max(skip_frame_values)
    min_skip = min(skip_frame_values)
    avg_skip = sum(skip_frame_values) / len(skip_frame_values)
    
    stats_text = f'Max: {max_skip}\nMin: {min_skip}\nAvg: {avg_skip:.2f}\nPoints: {len(skip_frame_values)}'
    plt.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    plt.savefig('skip_frame_plot.png', dpi=300, bbox_inches='tight')
    print("Chart saved as: skip_frame_plot.png")
    
    # 显示图表
    plt.show()
    
    # 打印统计信息
    print(f"\nStatistics:")
    print(f"Total data points: {len(skip_frame_values)}")
    print(f"Time range: {timestamps[0].strftime('%H:%M:%S')} to {timestamps[-1].strftime('%H:%M:%S')}")
    print(f"skip_frame max: {max_skip}")
    print(f"skip_frame min: {min_skip}")
    print(f"skip_frame avg: {avg_skip:.2f}")

if __name__ == "__main__":
    extract_and_plot()
