Jan 25 00:00:00 ar_logcat: Run Ar_logcat service.
Jan 25 00:00:00 ar_logcat: 5hJk2Ld8Rt9sFpQw^M
Jan 25 00:00:00 ar_logcat: vin start @ sdk version [5hJk2Ld8Rt9sFpQw] 
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390385][0296][0x7f9b09e200][SYS_CORE][VI_KEY] server.c: start_camera_server : 3693 load vin driver start^[[0m
Jan 25 00:00:01 ar_logcat: ^[[31;22m[124390388][0296][0x7f9b09e200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [/usrdata/local/factory/tunning/dpvif/dp_scaler_lut.bin] failed!^[[0m
Jan 25 00:00:01 ar_logcat: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390390][0296][0x7f9b09e200][SYS_CORE][VI_KEY] server.c: start_camera_server : 3957 load vin driver end max_sensor_count=5^[[0m
Jan 25 00:00:01 ar_logcat: Create socked success
Jan 25 00:00:01 ar_logcat: Bind socket success
Jan 25 00:00:01 ar_logcat: Listen socket success
Jan 25 00:00:01 ar_logcat: client accept thread running
Jan 25 00:00:01 ar_logcat: ^[[31;22m[25 00:00:00.932539][0368][0x7facd651d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnabl
Jan 25 00:00:01 ar_logcat: ^[[31;22m[25 00:00:00.933079][0368][0x7facd651d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:00:01 ar_logcat: ^[[31;22m[25 00:00:00.933811][0368][0x7facd651d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:00:01 ar_logcat: ^[[31;22m[25 00:00:00.933857][0368][0x7facd651d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390391][0296][0x7f9a05a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390391][0296][0x7f9ac7b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390391][0296][0x7f9ac7b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390391][0296][0x7f9ac7b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390391][0296][0x7f99f62200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390391][0296][0x7f99f62200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:01 ar_logcat: w h change from 0 0  to 1920 1080 fetch scaler lut 
Jan 25 00:00:01 ar_logcat: ^[[31;22m[124390391][0296][0x7f9ac7b200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [/usrdata/local/factory/tunning/dpvif/dp_scaler_lut.bin] failed!^[[0m
Jan 25 00:00:01 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:00:01 ar_logcat: 36823000 ^[[35;22m[124390391][0296][0x7f99f62200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:01 ar_logcat: 36a41000 36aca000 0 0 0 36b4d000 36d6b000 36df4000 0 0 0 
Jan 25 00:00:01 ar_logcat: aec index 512 line 1125 gain 1.000000 
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390392][0296][0x7f9ac7b200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390392][0296][0x7f9ac7b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390392][0296][0x7f9b09e200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390392][0296][0x7f9b09e200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:01 ar_logcat: ^[[33;22m[124390396][0296][0x7f9ac7b200][AIO_CORE][WARN] i2s.c: ar_i2s_io_dev_dma_start : 1345 Ao s32ChnEnCnt = 1 u32ChnCnt 2.
Jan 25 00:00:01 ar_logcat: ^[[0m
Jan 25 00:00:01 ar_logcat: ^[[33;22m[124390398][0296][0x7f9b09e200][AIO_CORE][WARN] i2s.c: ar_i2s_io_dev_dma_start : 1345 Ai s32ChnEnCnt = 1 u32ChnCnt 2.
Jan 25 00:00:01 ar_logcat: ^[[0m
Jan 25 00:00:01 ar_logcat: ^[[33;22m[124390446][0296][0x7f9ac7b200][VO_CORE][WARN] display_api.c: dc_init : 2267 use dc_reg_irq_by_cmd!^[[0m
Jan 25 00:00:01 ar_logcat: ^[[31;22m[124390447][0296][0x7f9ac7b200][VO_CORE][ERROR] display_top.c: ar_display_dev_enable : 3521 not support set irq_type:][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390448][0296][0x7f996c1200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390448][0296][0x7f995dc200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:01 ar_logcat: w h change from 0 0  to 1920 1080 fetch scaler lut 
Jan 25 00:00:01 ar_logcat: ^[[31;22m[124390448][0296][0x7f995dc200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:01 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:00:01 ar_logcat: 36e7d000 3709b000 37124000 0 0 0 371a7000 373c5000 3744e000 0 0 0 
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390448][0296][0x7f99f62200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390448][0296][0x7f99f62200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390448][0296][0x7f99f62200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:01 ar_logcat: aec index 4097 line 1125 gain 1.000000 
Jan 25 00:00:01 ar_logcat: 374d7000 376f5000 3777e000 0 0 0 37801000 37a1f000 37aa8000 0 0 0 
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390448][0296][0x7f995dc200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390448][0296][0x7f995dc200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390449][0296][0x7f9a332200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:00:01 ar_logcat: w h change from 1920 1080  to 1920 1080 fetch scaler lut 
Jan 25 00:00:01 ar_logcat: ^[[31;22m[124390449][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:01 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:00:01 ar_logcat: ^[[35;22m[124390450][0296][0x7f9a332200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:00:03 ar_logcat: open the driver sns:imx681 obj:stSnsimx681Obj lib:libsns_imx681.so 
Jan 25 00:00:03 ar_logcat: ^[[31;22m[25 00:00:03.220328][0368][0x7f98ff91d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3554 !!!!!!!!!! Note:your app cfg the isp read and vif write  as no compress,this mode isp read and vif write will cost more 
Jan 25 00:00:03 ar_logcat: ^[[31;22m[25 00:00:03.220515][0368][0x7f98ff91d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3657 stream id 0, no need cfg the sns_var_info^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390619][0296][0x7f99558200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=0^[[0m
Jan 25 00:00:03 ar_logcat: ^[[33;22m[124390621][0296][0x7f9b09e200][VO_CORE][WARN] display_api.c: dc_deinit : 2332 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:00:03 ar_logcat: ^[[33;22m[124390621][0296][0x7f99558200][VO_CORE][WARN] display_api.c: dc_deinit : 2332 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390621][0296][0x7f9b09e200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390621][0296][0x7f9b09e200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390622][0296][0x7f99558200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_close_camera : 961 camera_id=1^[[0m
Jan 25 00:00:03 ar_logcat: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:00:03 ar_logcat: ^[[31;22m[25 00:00:03.266158][0368][0x7facd651d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnabl
Jan 25 00:00:03 ar_logcat: ^[[31;22m[25 00:00:03.266189][0368][0x7facd651d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:00:03 ar_logcat: ^[[31;22m[25 00:00:03.266297][0368][0x7facd651d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:00:03 ar_logcat: ^[[31;22m[25 00:00:03.266306][0368][0x7facd651d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390624][0296][0x7f9b09e200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390624][0296][0x7f9a05a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390624][0296][0x7f9a05a200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390624][0296][0x7f99579200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390624][0296][0x7f99f62200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390624][0296][0x7f99f62200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390624][0296][0x7f99f62200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:03 ar_logcat: w h change from 0 0  to 1920 1080 fetch scaler lut 
Jan 25 00:00:03 ar_logcat: ^[[31;22m[124390624][0296][0x7f99579200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:03 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:00:03 ar_logcat: 36e7d000 3709b000 37124000 0 0 0 371a7000 373c5000 3744e000 0 0 0 
Jan 25 00:00:03 ar_logcat: aec index 4 line 1125 gain 1.000000 
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390624][0296][0x7f99579200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390624][0296][0x7f99579200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:03 ar_logcat: 374d7000 376f5000 3777e000 0 0 0 37801000 37a1f000 37aa8000 0 0 0 
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390626][0296][0x7f9a332200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390626][0296][0x7f9a332200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390627][0296][0x7f99579200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=0^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390627][0296][0x7f99579200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390627][0296][0x7f99579200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=0 stream_id=0^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390627][0296][0x7f99f62200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4384 power on vc 0 0^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390630][0296][0x7f99f62200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4386 power on vc end 0^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390637][0296][0x7f99f62200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390637][0296][0x7f99579200][SYS_CORE][VI_KEY] mipi_rx_9411.c: mipi_rx_power_up_9411 : 64 enable the mipi cfg and pcs clock, and set pcs to clk=100000000hz^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390637][0296][0x7f99579200][SYS_CORE][VI_KEY] mipi_rx.c: one_mipi_init : 1228 mipi 4 init done^[[0m
Jan 25 00:00:03 ar_logcat: aec index 4112 line 7042 gain 1.000000 
Jan 25 00:00:03 ar_logcat:  imx681_stream_on
Jan 25 00:00:03 ar_logcat:  imx681_trigger_on
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390637][0296][0x7f99579200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390637][0296][0x7f99558200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=0^[[0m
Jan 25 00:00:03 ar_logcat: ^[[31;22m[124390637][0296][0x7f99558200][VI_CORE][ERROR] rx_filter_port.c: rx_filter_get_link_res : 304 pick stream err^[[0m
Jan 25 00:00:03 ar_logcat: ^[[31;22m[124390637][0296][0x7f99558200][VI_CORE][ERROR] vif_in.c: vif_inlib_set_ctl : 304 the vif input lib [16] is already used by other vc channel^[[0m
Jan 25 00:00:03 ar_logcat: ^[[31;22m[124390637][0296][0x7f99558200][VI_CORE][ERROR] stream.c: link_filter_external : 407 check_linking_stream error:-2146402228^[[0m
Jan 25 00:00:03 ar_logcat: ^[[31;22m[124390637][0296][0x7f99558200][VI_CORE][ERROR] rx_filter_port.c: rx_filter_get_link_res : 304 pick stream err^[[0m
Jan 25 00:00:03 ar_logcat: ^[[31;22m[124390637][0296][0x7f99558200][VI_CORE][ERROR] vif_in.c: vif_inlib_set_ctl : 304 the vif input lib [16] is already used by other vc channel^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390638][0296][0x7f99558200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=1^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390638][0296][0x7f99579200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=0 stream_id=1^[[0m
Jan 25 00:00:03 ar_logcat: aec index 1 line 7042 gain 1.000000 
Jan 25 00:00:03 ar_logcat:  imx681_stream_on
Jan 25 00:00:03 ar_logcat:  imx681_trigger_on
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390638][0296][0x7f99579200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:03 ar_logcat: ^[[31;22m[25 00:00:03.409597][0368][0x7f98ff91d0][VI_HAL][ERROR] ar_hal_vin.c: ar_hal_vin_get_3a_info : 3625 Invalid fd -1
Jan 25 00:00:03 ar_logcat: ^[[0m
Jan 25 00:00:03 ar_logcat: ^[[31;22m[25 00:00:03.409715][0368][0x7f98ff91d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetAecManuTidyAttr : 12733 isp not runing, can not set prop^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390641][0296][0x7f99fc5200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390641][0296][0x7f99fc5200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:00:03 ar_logcat: ^[[31;22m[124390644][0529][0x7f7f511010][VPSS_MPP][ERROR] mpi_vpss.c: ar_hal_vpss_create : 870 bind_register_sender failed 0x80028003^[[0m
Jan 25 00:00:03 ar_logcat: ^[[33;22m[124390644][0296][0x7f99fc5200][VO_CORE][WARN] display_api.c: dc_init : 2267 use dc_reg_irq_by_cmd!^[[0m
Jan 25 00:00:03 ar_logcat: ^[[33;22m[124390644][0296][0x7f99fc5200][SYS_CORE][WARN] ar_subvision_ctrl.c: ar_subvision_vsync_statistic_disable : 483 subvision already disable!^[[0m
Jan 25 00:00:03 ar_logcat: ^[[31;22m[124390644][0296][0x7f99fc5200][VO_CORE][ERROR] display_top.c: ar_display_dev_enable : 3521 not support set irq_type:][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1125 stable ,ento ddr view 0^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390644][0296][0x7f9a332200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1125 stable ,ento ddr view 2^[[0m
Jan 25 00:00:03 ar_logcat: ^[[33;22m[124390644][0296][0x7f99fc5200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:00:03 ar_logcat: ^[[33;22m[124390644][0296][0x7f996c1200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390644][0296][0x7f99fc5200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390645][0296][0x7f99fc5200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390645][0296][0x7f99fc5200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390645][0296][0x7f99f62200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:00:03 ar_logcat: w h change from 0 0  to 1920 1080 fetch scaler lut 
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390645][0296][0x7f99f62200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390645][0296][0x7f99f62200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:00:03 ar_logcat: ^[[31;22m[124390645][0296][0x7f99fc5200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:03 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:00:03 ar_logcat: 36e7d000 3709b000 37124000 0 0 0 371a7000 373c5000 3744e000 0 0 0 
Jan 25 00:00:03 ar_logcat: aec index -1 line 1125 gain 1.000000 
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390645][0296][0x7f99fc5200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390645][0296][0x7f99fc5200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:00:03 ar_logcat: w h change from 1920 1080  to 1920 1080 fetch scaler lut 
Jan 25 00:00:03 ar_logcat: ^[[31;22m[124390646][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:03 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:00:03 ar_logcat: 374d7000 376f5000 3777e000 0 0 0 37801000 37a1f000 37aa8000 0 0 0 
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390646][0296][0x7f9a332200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:00:03 ar_logcat: ^[[33;22m[124390647][0296][0x7f996e2200][SYS_CORE][WARN] ar_subvision_ctrl.c: ar_subvision_vsync_statistic_enable : 456 subvision already enable!^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390647][0296][0x7f9a332200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:00:03 ar_logcat: ^[[33;22m[124390649][0296][0x7f996e2200][VO_CORE][WARN] display_top.c: display_offset_vo_src : 1520 hardware_diff_us:4922.886719 first_skewing_us:4922.886719!
Jan 25 00:00:03 ar_logcat: ^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390654][0296][0x7f9a332200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1173 cam_id[0], first frame done 1^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390654][0296][0x7f9a332200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1173 cam_id[0], first frame done 1^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390658][0296][0x7f9a332200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1173 cam_id[0], first frame done 2^[[0m
Jan 25 00:00:03 ar_logcat: ^[[35;22m[124390658][0296][0x7f9a332200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1173 cam_id[0], first frame done 2^[[0m
Jan 25 00:00:03 ar_logcat: w h change from 1920 1080  to 64 36 fetch scaler lut 
Jan 25 00:00:03 ar_logcat: ^[[31;22m[124390666][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:03 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:00:03 ar_logcat: w h change from 64 36  to 1600 900 fetch scaler lut 
Jan 25 00:00:03 ar_logcat: ^[[31;22m[124390667][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:03 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:00:52 ar_logcat: w h change from 1600 900  to 1600 1080 fetch scaler lut 
Jan 25 00:00:52 ar_logcat: ^[[31;22m[124395525][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:52 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:00:52 ar_logcat: w h change from 1600 1080  to 1920 1080 fetch scaler lut 
Jan 25 00:00:52 ar_logcat: ^[[31;22m[124395537][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:52 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:00:52 ar_logcat: w h change from 1920 1080  to 64 36 fetch scaler lut 
Jan 25 00:00:52 ar_logcat: ^[[31;22m[124395552][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:00:52 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:05 ar_logcat: w h change from 64 36  to 1600 900 fetch scaler lut 
Jan 25 00:02:05 ar_logcat: ^[[31;22m[124402807][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:05 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:07 ar_logcat: w h change from 1600 900  to 1600 1080 fetch scaler lut 
Jan 25 00:02:07 ar_logcat: ^[[31;22m[124403011][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:07 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:07 ar_logcat: w h change from 1600 1080  to 1600 900 fetch scaler lut 
Jan 25 00:02:07 ar_logcat: ^[[31;22m[124403034][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:07 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:08 ar_logcat: w h change from 1600 900  to 1600 1080 fetch scaler lut 
Jan 25 00:02:08 ar_logcat: ^[[31;22m[124403112][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:08 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:09 ar_logcat: w h change from 1600 1080  to 1600 900 fetch scaler lut 
Jan 25 00:02:09 ar_logcat: ^[[31;22m[124403231][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:09 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:11 ar_logcat: w h change from 1600 900  to 1920 900 fetch scaler lut 
Jan 25 00:02:11 ar_logcat: ^[[31;22m[124403421][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:11 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:11 ar_logcat: w h change from 1920 900  to 1920 1080 fetch scaler lut 
Jan 25 00:02:11 ar_logcat: ^[[31;22m[124403422][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:11 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:02:11 ar_logcat: w h change from 1920 1080  to 1600 1080 fetch scaler lut 
Jan 25 00:02:11 ar_logcat: ^[[31;22m[124403448][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:11 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:12 ar_logcat: w h change from 1600 1080  to 1600 900 fetch scaler lut 
Jan 25 00:02:12 ar_logcat: ^[[31;22m[124403516][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:12 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:12 ar_logcat: w h change from 1600 900  to 1920 900 fetch scaler lut 
Jan 25 00:02:12 ar_logcat: ^[[31;22m[124403547][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:12 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:12 ar_logcat: w h change from 1920 900  to 1600 900 fetch scaler lut 
Jan 25 00:02:12 ar_logcat: ^[[31;22m[124403555][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:12 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:13 ar_logcat: w h change from 1600 900  to 1600 1080 fetch scaler lut 
Jan 25 00:02:13 ar_logcat: ^[[31;22m[124403620][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:13 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:13 ar_logcat: w h change from 1600 1080  to 1600 900 fetch scaler lut 
Jan 25 00:02:13 ar_logcat: ^[[31;22m[124403633][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:13 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:13 ar_logcat: w h change from 1600 900  to 1600 1080 fetch scaler lut 
Jan 25 00:02:13 ar_logcat: ^[[31;22m[124403662][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:13 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:17 ar_logcat: w h change from 1600 1080  to 1600 900 fetch scaler lut 
Jan 25 00:02:17 ar_logcat: ^[[31;22m[124404057][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:17 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:18 ar_logcat: w h change from 1600 900  to 1600 1080 fetch scaler lut 
Jan 25 00:02:18 ar_logcat: ^[[31;22m[124404116][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:18 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:18 ar_logcat: w h change from 1600 1080  to 1600 900 fetch scaler lut 
Jan 25 00:02:18 ar_logcat: ^[[31;22m[124404141][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:18 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:19 ar_logcat: w h change from 1600 900  to 1600 1080 fetch scaler lut 
Jan 25 00:02:19 ar_logcat: ^[[31;22m[124404261][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:19 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:20 ar_logcat: w h change from 1600 1080  to 1600 900 fetch scaler lut 
Jan 25 00:02:20 ar_logcat: ^[[31;22m[124404324][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:20 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:20 ar_logcat: w h change from 1600 900  to 1600 1080 fetch scaler lut 
Jan 25 00:02:20 ar_logcat: ^[[31;22m[124404367][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:20 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:21 ar_logcat: w h change from 1600 1080  to 1600 900 fetch scaler lut 
Jan 25 00:02:21 ar_logcat: ^[[31;22m[124404430][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:21 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:21 ar_logcat: w h change from 1600 900  to 1600 1080 fetch scaler lut 
Jan 25 00:02:21 ar_logcat: ^[[31;22m[124404451][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:21 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:22 ar_logcat: w h change from 1600 1080  to 1600 900 fetch scaler lut 
Jan 25 00:02:22 ar_logcat: ^[[31;22m[124404541][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:22 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:26 ar_logcat: w h change from 1600 900  to 1600 1080 fetch scaler lut 
Jan 25 00:02:26 ar_logcat: ^[[31;22m[124404965][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:26 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:28 ar_logcat: w h change from 1600 1080  to 1600 900 fetch scaler lut 
Jan 25 00:02:28 ar_logcat: ^[[31;22m[124405097][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:28 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:39 ar_logcat: w h change from 1600 900  to 1600 1080 fetch scaler lut 
Jan 25 00:02:39 ar_logcat: ^[[31;22m[124406220][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:39 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:39 ar_logcat: w h change from 1600 1080  to 1600 900 fetch scaler lut 
Jan 25 00:02:39 ar_logcat: ^[[31;22m[124406274][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:39 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:40 ar_logcat: w h change from 1600 900  to 1600 1080 fetch scaler lut 
Jan 25 00:02:40 ar_logcat: ^[[31;22m[124406292][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:40 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:40 ar_logcat: w h change from 1600 1080  to 1600 900 fetch scaler lut 
Jan 25 00:02:40 ar_logcat: ^[[31;22m[124406321][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:40 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:40 ar_logcat: w h change from 1600 900  to 1600 1080 fetch scaler lut 
Jan 25 00:02:40 ar_logcat: ^[[31;22m[124406343][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:40 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:41 ar_logcat: w h change from 1600 1080  to 1600 900 fetch scaler lut 
Jan 25 00:02:41 ar_logcat: ^[[31;22m[124406442][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:41 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:41 ar_logcat: w h change from 1600 900  to 1600 1080 fetch scaler lut 
Jan 25 00:02:41 ar_logcat: ^[[31;22m[124406482][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:41 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:58 ar_logcat: w h change from 1600 1080  to 1600 900 fetch scaler lut 
Jan 25 00:02:58 ar_logcat: ^[[31;22m[124408089][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:58 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:02:59 ar_logcat: w h change from 1600 900  to 1600 1080 fetch scaler lut 
Jan 25 00:02:59 ar_logcat: ^[[31;22m[124408232][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:02:59 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:03:01 ar_logcat: w h change from 1600 1080  to 1920 1080 fetch scaler lut 
Jan 25 00:03:01 ar_logcat: ^[[31;22m[124408396][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:03:01 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:03:01 ar_logcat: w h change from 1920 1080  to 64 36 fetch scaler lut 
Jan 25 00:03:01 ar_logcat: ^[[31;22m[124408399][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:03:01 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:03:01 ar_logcat: w h change from 64 36  to 1920 1080 fetch scaler lut 
Jan 25 00:03:01 ar_logcat: ^[[31;22m[124408480][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:03:01 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:03:02 ar_logcat: w h change from 1920 1080  to 64 36 fetch scaler lut 
Jan 25 00:03:02 ar_logcat: ^[[31;22m[124408521][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:03:02 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:03:04 ar_logcat: w h change from 64 36  to 1920 1080 fetch scaler lut 
Jan 25 00:03:04 ar_logcat: ^[[31;22m[124408761][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:03:04 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:03:05 ar_logcat: w h change from 1920 1080  to 1920 900 fetch scaler lut 
Jan 25 00:03:05 ar_logcat: ^[[31;22m[124408826][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:03:05 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:03:05 ar_logcat: ^[[33;22m[124408881][0296][0x7f99fc5200][VO_CORE][WARN] display_api.c: dc_deinit : 2332 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:03:05 ar_logcat: ^[[33;22m[124408881][0296][0x7f9965c200][SYS_CORE][WARN] ar_subvision_ctrl.c: ar_subvision_vsync_statistic_disable : 483 subvision already disable!^[[0m
Jan 25 00:03:05 ar_logcat: ^[[33;22m[124408881][0296][0x7f9965c200][VO_CORE][WARN] display_api.c: dc_deinit : 2332 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:03:05 ar_logcat: ^[[35;22m[124408882][0296][0x7f995fd200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:03:05 ar_logcat: ^[[35;22m[124408883][0296][0x7f995fd200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:03:05 ar_logcat: ^[[35;22m[124408883][0296][0x7f99579200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_close_camera : 961 camera_id=1^[[0m
Jan 25 00:03:05 ar_logcat: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:03:05 ar_logcat: ^[[31;22m[25 00:03:05.866847][0368][0x7facd651d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnabl
Jan 25 00:03:05 ar_logcat: ^[[31;22m[25 00:03:05.866876][0368][0x7facd651d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:03:05 ar_logcat: ^[[31;22m[25 00:03:05.866985][0368][0x7facd651d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:03:05 ar_logcat: ^[[31;22m[25 00:03:05.867015][0368][0x7facd651d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:03:05 ar_logcat: ^[[35;22m[124408884][0296][0x7f996c1200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:03:05 ar_logcat: ^[[35;22m[124408884][0296][0x7f9965c200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:03:05 ar_logcat: ^[[35;22m[124408884][0296][0x7f9965c200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:03:05 ar_logcat: ^[[35;22m[124408885][0296][0x7f99579200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:03:05 ar_logcat: ^[[35;22m[124408885][0296][0x7f99f62200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:03:05 ar_logcat: w h change from 0 0  to 1920 1080 fetch scaler lut 
Jan 25 00:03:05 ar_logcat: ^[[31;22m[124408885][0296][0x7f99579200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:03:05 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:03:05 ar_logcat: ^[[35;22m[124408885][0296][0x7f99f62200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:03:05 ar_logcat: 36e7d000 3709b000 37124000 0 0 0 371a7000 373c5000 3744e000 0 0 0 
Jan 25 00:03:05 ar_logcat: ^[[35;22m[124408885][0296][0x7f99f62200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:03:05 ar_logcat: aec index 69206016 line 1125 gain 1.000000 
Jan 25 00:03:05 ar_logcat: ^[[35;22m[124408885][0296][0x7f99579200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:03:05 ar_logcat: ^[[35;22m[124408885][0296][0x7f99579200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:03:05 ar_logcat: 374d7000 376f5000 3777e000 0 0 0 37801000 37a1f000 37aa8000 0 0 0 
Jan 25 00:03:05 ar_logcat: ^[[35;22m[124408886][0296][0x7f9a332200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:03:05 ar_logcat: ^[[35;22m[124408886][0296][0x7f9a332200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:03:06 ar_logcat: ^[[35;22m[124408894][0296][0x7f99412200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=0 stream_id=0^[[0m
Jan 25 00:03:06 ar_logcat: ^[[35;22m[124408895][0296][0x7f99412200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=0 stream_id=0 exit^[[0m
Jan 25 00:03:06 ar_logcat: ^[[35;22m[124408896][0296][0x7f99454200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=0 stream_id=1^[[0m
Jan 25 00:03:06 ar_logcat: ^[[35;22m[124408897][0296][0x7f99454200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=0 stream_id=1 exit^[[0m
Jan 25 00:03:06 ar_logcat: ^[[35;22m[124408897][0296][0x7f995fd200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_close_camera : 961 camera_id=0^[[0m
Jan 25 00:03:06 ar_logcat: ^[[35;22m[124408901][0296][0x7f996c1200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:03:06 ar_logcat: ^[[35;22m[124408901][0296][0x7f996c1200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:03:06 ar_logcat: ^[[31;22m[124408904][0529][0x7f7e85c1a0][VPSS_MPP][ERROR] mpi_vpss.c: ar_hal_vpss_create : 870 bind_register_sender failed 0x80028003^[[0m
Jan 25 00:03:06 ar_logcat: ^[[33;22m[124408904][0296][0x7f995dc200][VO_CORE][WARN] display_api.c: dc_init : 2267 use dc_reg_irq_by_cmd!^[[0m
Jan 25 00:03:06 ar_logcat: ^[[33;22m[124408904][0296][0x7f995dc200][SYS_CORE][WARN] ar_subvision_ctrl.c: ar_subvision_vsync_statistic_disable : 483 subvision already disable!^[[0m
Jan 25 00:03:06 ar_logcat: ^[[31;22m[124408904][0296][0x7f995dc200][VO_CORE][ERROR] display_top.c: ar_display_dev_enable : 3521 not support set irq_type:isable!^[[0m
Jan 25 00:03:06 ar_logcat: ^[[33;22m[124408904][0296][0x7f995dc200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:03:06 ar_logcat: ^[[33;22m[124408904][0296][0x7f996c1200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:03:06 ar_logcat: ^[[35;22m[124408904][0296][0x7f99475200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:03:06 ar_logcat: ^[[35;22m[124408905][0296][0x7f99475200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:03:06 ar_logcat: ^[[35;22m[124408905][0296][0x7f993f1200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:03:06 ar_logcat: ^[[35;22m[124408905][0296][0x7f99f62200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:03:06 ar_logcat: ^[[35;22m[124408905][0296][0x7f99f62200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:03:06 ar_logcat: ^[[35;22m[124408905][0296][0x7f99f62200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:03:06 ar_logcat: w h change from 0 0  to 1920 1080 fetch scaler lut 
Jan 25 00:03:06 ar_logcat: ^[[31;22m[124408905][0296][0x7f993f1200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:03:06 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:03:06 ar_logcat: 36e7d000 3709b000 37124000 0 0 0 371a7000 373c5000 3744e000 0 0 0 
Jan 25 00:03:06 ar_logcat: aec index 1142947840 line 1125 gain 1.000000 
Jan 25 00:03:06 ar_logcat: ^[[35;22m[124408905][0296][0x7f993f1200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:03:06 ar_logcat: ^[[35;22m[124408905][0296][0x7f993f1200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:03:06 ar_logcat: w h change from 1920 1080  to 1920 1080 fetch scaler lut 
Jan 25 00:03:06 ar_logcat: ^[[31;22m[124408906][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:03:06 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:03:06 ar_logcat: 374d7000 376f5000 3777e000 0 0 0 37801000 37a1f000 37aa8000 0 0 0 
Jan 25 00:03:06 ar_logcat: ^[[35;22m[124408906][0296][0x7f9a332200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:03:06 ar_logcat: ^[[33;22m[124408907][0296][0x7f996e2200][SYS_CORE][WARN] ar_subvision_ctrl.c: ar_subvision_vsync_statistic_enable : 456 subvision already enable!^[[0m
Jan 25 00:03:06 ar_logcat: ^[[35;22m[124408907][0296][0x7f9a332200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:03:06 ar_logcat: ^[[33;22m[124408909][0296][0x7f996e2200][VO_CORE][WARN] display_top.c: display_offset_vo_src : 1520 hardware_diff_us:4922.879883 first_skewing_us:4922.879883!
Jan 25 00:03:06 ar_logcat: ^[[0m
Jan 25 00:03:06 ar_logcat: w h change from 1920 1080  to 1920 900 fetch scaler lut 
Jan 25 00:03:06 ar_logcat: ^[[31;22m[124408909][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:03:06 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:03:06 ar_logcat: w h change from 1920 900  to 1600 900 fetch scaler lut 
Jan 25 00:03:06 ar_logcat: ^[[31;22m[124408910][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:03:06 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:04:05 ar_logcat: w h change from 1600 900  to 1600 1080 fetch scaler lut 
Jan 25 00:04:05 ar_logcat: ^[[31;22m[124414878][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:04:05 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:04:05 ar_logcat: w h change from 1600 1080  to 1920 1080 fetch scaler lut 
Jan 25 00:04:05 ar_logcat: ^[[31;22m[124414881][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:04:05 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:16:10 ar_logcat: open the driver sns:imx681 obj:stSnsimx681Obj lib:libsns_imx681.so 
Jan 25 00:16:10 ar_logcat: ^[[33;22m[124487312][0296][0x7f99475200][VO_CORE][WARN] display_api.c: dc_deinit : 2332 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:16:10 ar_logcat: ^[[33;22m[124487313][0296][0x7f99412200][SYS_CORE][WARN] ar_subvision_ctrl.c: ar_subvision_vsync_statistic_disable : 483 subvision already disable!^[[0m
Jan 25 00:16:10 ar_logcat: ^[[33;22m[124487313][0296][0x7f99412200][VO_CORE][WARN] display_api.c: dc_deinit : 2332 use dc_unreg_irq_by_cmd!^[[0m
Jan 25 00:16:10 ar_logcat: ^[[31;22m[25 00:16:10.157378][0368][0x7f98ff91d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3554 !!!!!!!!!! Note:your app cfg the isp read and vif write  as no compress,this mode isp read and vif write will cost more 
Jan 25 00:16:10 ar_logcat: ^[[31;22m[25 00:16:10.157576][0368][0x7f98ff91d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3657 stream id 0, no need cfg the sns_var_info^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487313][0296][0x7f996c1200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487313][0296][0x7f99412200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=0^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487314][0296][0x7f996c1200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487315][0296][0x7f99454200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_close_camera : 961 camera_id=1^[[0m
Jan 25 00:16:10 ar_logcat: open the driver sns:dp_rx obj:stSnsdp_rxObj lib:libsns_dp_rx.so 
Jan 25 00:16:10 ar_logcat: ^[[31;22m[25 00:16:10.181396][0368][0x7facd651d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3559 !!!!!!the vif is force to dpcm duce to reduce bandwidth, if you want to use the dng, please set the pstPipeAttr->enEnabl
Jan 25 00:16:10 ar_logcat: ^[[31;22m[25 00:16:10.181428][0368][0x7facd651d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_VI_CreatePipe : 3603 !!!!!!! Note: your app cfg the vif write compress on,so you must cfg the vif fre >=3*mipi fre !!!!!!^[[0m
Jan 25 00:16:10 ar_logcat: ^[[31;22m[25 00:16:10.181608][0368][0x7facd651d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7846 not supported addr type^[[0m
Jan 25 00:16:10 ar_logcat: ^[[31;22m[25 00:16:10.181619][0368][0x7facd651d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetPubAttr : 7856 not supported data type^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487315][0296][0x7f99579200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_open_camera : 920 camera_id=1^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487315][0296][0x7f99558200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487316][0296][0x7f99558200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487316][0296][0x7f99454200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487316][0296][0x7f99f62200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487316][0296][0x7f99f62200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487316][0296][0x7f99f62200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:16:10 ar_logcat: w h change from 0 0  to 1920 1080 fetch scaler lut 
Jan 25 00:16:10 ar_logcat: ^[[31;22m[124487316][0296][0x7f99454200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:16:10 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:16:10 ar_logcat: 36e7d000 3709b000 37124000 0 0 0 371a7000 373c5000 3744e000 0 0 0 
Jan 25 00:16:10 ar_logcat: aec index 65539 line 1125 gain 1.000000 
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487316][0296][0x7f99454200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487316][0296][0x7f99454200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:16:10 ar_logcat: 374d7000 376f5000 3777e000 0 0 0 37801000 37a1f000 37aa8000 0 0 0 
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487317][0296][0x7f9a332200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487318][0296][0x7f9a332200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487321][0296][0x7f996c1200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=0^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487321][0296][0x7f996c1200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487321][0296][0x7f99412200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=0 stream_id=0^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487321][0296][0x7f99f62200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4384 power on vc 0 0^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487324][0296][0x7f99f62200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4386 power on vc end 0^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487330][0296][0x7f99f62200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487331][0296][0x7f99412200][SYS_CORE][VI_KEY] mipi_rx_9411.c: mipi_rx_power_up_9411 : 64 enable the mipi cfg and pcs clock, and set pcs to clk=100000000hz^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487331][0296][0x7f99412200][SYS_CORE][VI_KEY] mipi_rx.c: one_mipi_init : 1228 mipi 4 init done^[[0m
Jan 25 00:16:10 ar_logcat: aec index -1 line 7042 gain 1.000000 
Jan 25 00:16:10 ar_logcat:  imx681_stream_on
Jan 25 00:16:10 ar_logcat:  imx681_trigger_on
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487331][0296][0x7f99412200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487331][0296][0x7f996c1200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=0^[[0m
Jan 25 00:16:10 ar_logcat: ^[[31;22m[124487331][0296][0x7f996c1200][VI_CORE][ERROR] rx_filter_port.c: rx_filter_get_link_res : 304 pick stream err^[[0m
Jan 25 00:16:10 ar_logcat: ^[[31;22m[124487331][0296][0x7f996c1200][VI_CORE][ERROR] vif_in.c: vif_inlib_set_ctl : 304 the vif input lib [16] is already used by other vc channel^[[0m
Jan 25 00:16:10 ar_logcat: ^[[31;22m[124487331][0296][0x7f996c1200][VI_CORE][ERROR] stream.c: link_filter_external : 407 check_linking_stream error:-2146402228^[[0m
Jan 25 00:16:10 ar_logcat: ^[[31;22m[124487331][0296][0x7f996c1200][VI_CORE][ERROR] rx_filter_port.c: rx_filter_get_link_res : 304 pick stream err^[[0m
Jan 25 00:16:10 ar_logcat: ^[[31;22m[124487331][0296][0x7f996c1200][VI_CORE][ERROR] vif_in.c: vif_inlib_set_ctl : 304 the vif input lib [16] is already used by other vc channel^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487331][0296][0x7f996c1200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=1^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487331][0296][0x7f99579200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=0 stream_id=1^[[0m
Jan 25 00:16:10 ar_logcat: aec index 2147483647 line 7042 gain 1.000000 
Jan 25 00:16:10 ar_logcat:  imx681_stream_on
Jan 25 00:16:10 ar_logcat:  imx681_trigger_on
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487331][0296][0x7f99579200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:16:10 ar_logcat: ^[[31;22m[25 00:16:10.344206][0368][0x7f98ff91d0][VI_HAL][ERROR] ar_hal_vin.c: ar_hal_vin_get_3a_info : 3625 Invalid fd -1
Jan 25 00:16:10 ar_logcat: ^[[0m
Jan 25 00:16:10 ar_logcat: ^[[31;22m[25 00:16:10.344236][0368][0x7f98ff91d0][VI_HAL][ERROR] mpi_vin.c: AR_MPI_ISP_SetAecManuTidyAttr : 12733 isp not runing, can not set prop^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487332][0296][0x7f99579200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1538 camera_id=1 stream_id=0^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487332][0296][0x7f99579200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_stop_stream : 1568 camera_id=1 stream_id=0 exit^[[0m
Jan 25 00:16:10 ar_logcat: ^[[31;22m[124487335][0529][0x7f7e85c1a0][VPSS_MPP][ERROR] mpi_vpss.c: ar_hal_vpss_create : 870 bind_register_sender failed 0x80028003^[[0m
Jan 25 00:16:10 ar_logcat: ^[[33;22m[124487335][0296][0x7f99537200][VO_CORE][WARN] display_api.c: dc_init : 2267 use dc_reg_irq_by_cmd!^[[0m
Jan 25 00:16:10 ar_logcat: ^[[33;22m[124487335][0296][0x7f99537200][SYS_CORE][WARN] ar_subvision_ctrl.c: ar_subvision_vsync_statistic_disable : 483 subvision already disable!^[[0m
Jan 25 00:16:10 ar_logcat: ^[[31;22m[124487335][0296][0x7f99537200][VO_CORE][ERROR] display_top.c: ar_display_dev_enable : 3521 not support set irq_type:r_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:16:10 ar_logcat: ^[[33;22m[124487336][0296][0x7f99537200][AXIMGR_CORE][WARN] ar_aximgr_lb.c: ar_aximgr_lb_enable : 285 [LB] 1920 1024 1024^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487336][0296][0x7f9ac7b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2033 camera_id=1^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487336][0296][0x7f9ac7b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_creat_stream_man_link : 2145 exit stream_id=0^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487337][0296][0x7f9ac7b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1500 camera_id=1 stream_id=0^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487337][0296][0x7f99f62200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4373 power on 1^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487337][0296][0x7f99f62200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4375 power on end 1^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487337][0296][0x7f99f62200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_init : 1470 sensor init done^[[0m
Jan 25 00:16:10 ar_logcat: w h change from 0 0  to 1920 1080 fetch scaler lut 
Jan 25 00:16:10 ar_logcat: ^[[31;22m[124487337][0296][0x7f9ac7b200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:16:10 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:16:10 ar_logcat: 36e7d000 3709b000 37124000 0 0 0 371a7000 373c5000 3744e000 0 0 0 
Jan 25 00:16:10 ar_logcat: aec index -1 line 1125 gain 1.000000 
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487337][0296][0x7f9ac7b200][SYS_CORE][VI_KEY] cam_sensor.c: sensor_set_ctl : 4475 vin_dev_1 stream on done^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487337][0296][0x7f9ac7b200][SYS_CORE][VI_KEY] cam_api.c: ar_camera_start_stream : 1532 exit^[[0m
Jan 25 00:16:10 ar_logcat: w h change from 1920 1080  to 1920 1080 fetch scaler lut 
Jan 25 00:16:10 ar_logcat: ^[[31;22m[124487337][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:16:10 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:16:10 ar_logcat: 374d7000 376f5000 3777e000 0 0 0 37801000 37a1f000 37aa8000 0 0 0 
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487338][0296][0x7f9a332200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=1^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487338][0296][0x7f9a332200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1125 stable ,ento ddr view 0^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487338][0296][0x7f9a332200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1125 stable ,ento ddr view 2^[[0m
Jan 25 00:16:10 ar_logcat: ^[[33;22m[124487338][0296][0x7f99726200][SYS_CORE][WARN] ar_subvision_ctrl.c: ar_subvision_vsync_statistic_enable : 456 subvision already enable!^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487339][0296][0x7f9a332200][SYS_CORE][VI_KEY] dp_vif_out.c: dp_vif_buffer_done_no_lock : 6087 fbuffer done frame_id=2^[[0m
Jan 25 00:16:10 ar_logcat: ^[[33;22m[124487340][0296][0x7f996e2200][VO_CORE][WARN] display_top.c: display_offset_vo_src : 1520 hardware_diff_us:4922.873535 first_skewing_us:4922.873535!
Jan 25 00:16:10 ar_logcat: ^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487348][0296][0x7f9a332200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1173 cam_id[0], first frame done 1^[[0m
Jan 25 00:16:10 ar_logcat: ^[[35;22m[124487348][0296][0x7f9a332200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1173 cam_id[0], first frame done 1^[[0m
Jan 25 00:16:11 ar_logcat: ^[[35;22m[124487351][0296][0x7f9a332200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1173 cam_id[0], first frame done 2^[[0m
Jan 25 00:16:11 ar_logcat: ^[[35;22m[124487351][0296][0x7f9a332200][SYS_CORE][VI_KEY] vif_mid.c: vif_bpintr_process : 1173 cam_id[0], first frame done 2^[[0m
Jan 25 00:16:19 ar_logcat: w h change from 1920 1080  to 64 36 fetch scaler lut 
Jan 25 00:16:19 ar_logcat: ^[[31;22m[124488162][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:16:19 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:16:19 ar_logcat: w h change from 64 36  to 1920 1080 fetch scaler lut 
Jan 25 00:16:19 ar_logcat: ^[[31;22m[124488197][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:16:19 ar_logcat: out and in is equal, use y bypass lut table 
Jan 25 00:17:24 ar_logcat: w h change from 1920 1080  to 64 36 fetch scaler lut 
Jan 25 00:17:24 ar_logcat: ^[[31;22m[124494684][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:17:24 ar_logcat: out and in is not equal, use uvsmooth lut table 
Jan 25 00:17:25 ar_logcat: w h change from 64 36  to 1920 1080 fetch scaler lut 
Jan 25 00:17:25 ar_logcat: ^[[31;22m[124494758][0296][0x7f9a332200][VI_CORE][ERROR] dp_vif_out.c: dp_vif_read_lut : 4137 open file [] failed!^[[0m
Jan 25 00:17:25 ar_logcat: out and in is equal, use y bypass lut table 
